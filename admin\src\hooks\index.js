import { useState, useEffect, useCallback, useRef } from 'react';
import { message } from 'antd';
import { debounce } from '@/utils';

/**
 * 异步数据获取 Hook
 * @param {Function} asyncFunction - 异步函数
 * @param {Array} deps - 依赖数组
 * @param {object} options - 选项
 * @returns {object} { data, loading, error, refetch }
 */
export const useAsync = (asyncFunction, deps = [], options = {}) => {
  const {
    immediate = true,
    initialData = null,
    onSuccess,
    onError
  } = options;
  
  const [data, setData] = useState(initialData);
  const [loading, setLoading] = useState(immediate);
  const [error, setError] = useState(null);
  const mountedRef = useRef(true);
  
  const execute = useCallback(async (...args) => {
    try {
      setLoading(true);
      setError(null);
      
      const result = await asyncFunction(...args);
      
      if (mountedRef.current) {
        setData(result);
        onSuccess?.(result);
      }
      
      return result;
    } catch (err) {
      if (mountedRef.current) {
        setError(err);
        onError?.(err);
      }
      throw err;
    } finally {
      if (mountedRef.current) {
        setLoading(false);
      }
    }
  }, deps);
  
  useEffect(() => {
    if (immediate) {
      execute();
    }
  }, [execute, immediate]);
  
  useEffect(() => {
    return () => {
      mountedRef.current = false;
    };
  }, []);
  
  return {
    data,
    loading,
    error,
    execute,
    refetch: execute
  };
};

/**
 * 防抖 Hook
 * @param {any} value - 要防抖的值
 * @param {number} delay - 延迟时间
 * @returns {any} 防抖后的值
 */
export const useDebounce = (value, delay) => {
  const [debouncedValue, setDebouncedValue] = useState(value);
  
  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);
    
    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);
  
  return debouncedValue;
};

/**
 * 防抖回调 Hook
 * @param {Function} callback - 回调函数
 * @param {number} delay - 延迟时间
 * @param {Array} deps - 依赖数组
 * @returns {Function} 防抖后的回调函数
 */
export const useDebouncedCallback = (callback, delay, deps = []) => {
  return useCallback(
    debounce(callback, delay),
    [...deps, delay]
  );
};

/**
 * 本地存储 Hook
 * @param {string} key - 存储键
 * @param {any} initialValue - 初始值
 * @returns {Array} [value, setValue, removeValue]
 */
export const useLocalStorage = (key, initialValue) => {
  const [storedValue, setStoredValue] = useState(() => {
    try {
      const item = window.localStorage.getItem(key);
      return item ? JSON.parse(item) : initialValue;
    } catch (error) {
      console.error(`Error reading localStorage key "${key}":`, error);
      return initialValue;
    }
  });
  
  const setValue = useCallback((value) => {
    try {
      const valueToStore = value instanceof Function ? value(storedValue) : value;
      setStoredValue(valueToStore);
      window.localStorage.setItem(key, JSON.stringify(valueToStore));
    } catch (error) {
      console.error(`Error setting localStorage key "${key}":`, error);
    }
  }, [key, storedValue]);
  
  const removeValue = useCallback(() => {
    try {
      window.localStorage.removeItem(key);
      setStoredValue(initialValue);
    } catch (error) {
      console.error(`Error removing localStorage key "${key}":`, error);
    }
  }, [key, initialValue]);
  
  return [storedValue, setValue, removeValue];
};

/**
 * 会话存储 Hook
 * @param {string} key - 存储键
 * @param {any} initialValue - 初始值
 * @returns {Array} [value, setValue, removeValue]
 */
export const useSessionStorage = (key, initialValue) => {
  const [storedValue, setStoredValue] = useState(() => {
    try {
      const item = window.sessionStorage.getItem(key);
      return item ? JSON.parse(item) : initialValue;
    } catch (error) {
      console.error(`Error reading sessionStorage key "${key}":`, error);
      return initialValue;
    }
  });
  
  const setValue = useCallback((value) => {
    try {
      const valueToStore = value instanceof Function ? value(storedValue) : value;
      setStoredValue(valueToStore);
      window.sessionStorage.setItem(key, JSON.stringify(valueToStore));
    } catch (error) {
      console.error(`Error setting sessionStorage key "${key}":`, error);
    }
  }, [key, storedValue]);
  
  const removeValue = useCallback(() => {
    try {
      window.sessionStorage.removeItem(key);
      setStoredValue(initialValue);
    } catch (error) {
      console.error(`Error removing sessionStorage key "${key}":`, error);
    }
  }, [key, initialValue]);
  
  return [storedValue, setValue, removeValue];
};

/**
 * 窗口大小 Hook
 * @returns {object} { width, height, isMobile, isTablet, isDesktop }
 */
export const useWindowSize = () => {
  const [windowSize, setWindowSize] = useState({
    width: window.innerWidth,
    height: window.innerHeight
  });
  
  useEffect(() => {
    const handleResize = () => {
      setWindowSize({
        width: window.innerWidth,
        height: window.innerHeight
      });
    };
    
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);
  
  return {
    ...windowSize,
    isMobile: windowSize.width < 768,
    isTablet: windowSize.width >= 768 && windowSize.width < 1024,
    isDesktop: windowSize.width >= 1024
  };
};

/**
 * 点击外部区域 Hook
 * @param {Function} callback - 点击外部时的回调函数
 * @returns {React.RefObject} ref 对象
 */
export const useClickOutside = (callback) => {
  const ref = useRef();
  
  useEffect(() => {
    const handleClick = (event) => {
      if (ref.current && !ref.current.contains(event.target)) {
        callback();
      }
    };
    
    document.addEventListener('mousedown', handleClick);
    return () => document.removeEventListener('mousedown', handleClick);
  }, [callback]);
  
  return ref;
};

/**
 * 键盘快捷键 Hook
 * @param {string|Array} keys - 快捷键组合
 * @param {Function} callback - 回调函数
 * @param {object} options - 选项
 */
export const useKeyboard = (keys, callback, options = {}) => {
  const { target = document, event = 'keydown' } = options;
  
  useEffect(() => {
    const targetKeys = Array.isArray(keys) ? keys : [keys];
    
    const handleKeyPress = (e) => {
      const pressedKeys = [];
      
      if (e.ctrlKey) pressedKeys.push('ctrl');
      if (e.shiftKey) pressedKeys.push('shift');
      if (e.altKey) pressedKeys.push('alt');
      if (e.metaKey) pressedKeys.push('meta');
      
      pressedKeys.push(e.key.toLowerCase());
      
      const keyString = pressedKeys.join('+');
      
      if (targetKeys.includes(keyString)) {
        e.preventDefault();
        callback(e);
      }
    };
    
    target.addEventListener(event, handleKeyPress);
    return () => target.removeEventListener(event, handleKeyPress);
  }, [keys, callback, target, event]);
};

/**
 * 复制到剪贴板 Hook
 * @returns {Array} [copyToClipboard, { success, error }]
 */
export const useClipboard = () => {
  const [state, setState] = useState({ success: false, error: null });
  
  const copyToClipboard = useCallback(async (text) => {
    try {
      await navigator.clipboard.writeText(text);
      setState({ success: true, error: null });
      message.success('复制成功');
    } catch (error) {
      setState({ success: false, error });
      message.error('复制失败');
    }
  }, []);
  
  return [copyToClipboard, state];
};

/**
 * 表格选择 Hook
 * @param {object} options - 选项
 * @returns {object} 表格选择相关的状态和方法
 */
export const useTableSelection = (options = {}) => {
  const { rowKey = 'id' } = options;
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [selectedRows, setSelectedRows] = useState([]);
  
  const rowSelection = {
    selectedRowKeys,
    onChange: (keys, rows) => {
      setSelectedRowKeys(keys);
      setSelectedRows(rows);
    },
    onSelectAll: (selected, selectedRows, changeRows) => {
      if (selected) {
        const newKeys = selectedRows.map(row => row[rowKey]);
        setSelectedRowKeys(newKeys);
        setSelectedRows(selectedRows);
      } else {
        setSelectedRowKeys([]);
        setSelectedRows([]);
      }
    }
  };
  
  const clearSelection = useCallback(() => {
    setSelectedRowKeys([]);
    setSelectedRows([]);
  }, []);
  
  const selectAll = useCallback((data) => {
    const keys = data.map(row => row[rowKey]);
    setSelectedRowKeys(keys);
    setSelectedRows(data);
  }, [rowKey]);
  
  const selectRow = useCallback((row) => {
    const key = row[rowKey];
    if (selectedRowKeys.includes(key)) {
      setSelectedRowKeys(prev => prev.filter(k => k !== key));
      setSelectedRows(prev => prev.filter(r => r[rowKey] !== key));
    } else {
      setSelectedRowKeys(prev => [...prev, key]);
      setSelectedRows(prev => [...prev, row]);
    }
  }, [selectedRowKeys, rowKey]);
  
  return {
    selectedRowKeys,
    selectedRows,
    rowSelection,
    clearSelection,
    selectAll,
    selectRow,
    hasSelected: selectedRowKeys.length > 0,
    selectedCount: selectedRowKeys.length
  };
};

/**
 * 分页 Hook
 * @param {object} options - 选项
 * @returns {object} 分页相关的状态和方法
 */
export const usePagination = (options = {}) => {
  const {
    initialPage = 1,
    initialPageSize = 10,
    total = 0
  } = options;
  
  const [current, setCurrent] = useState(initialPage);
  const [pageSize, setPageSize] = useState(initialPageSize);
  
  const pagination = {
    current,
    pageSize,
    total,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
    onChange: (page, size) => {
      setCurrent(page);
      if (size !== pageSize) {
        setPageSize(size);
      }
    },
    onShowSizeChange: (current, size) => {
      setPageSize(size);
      setCurrent(1); // 改变页面大小时回到第一页
    }
  };
  
  const reset = useCallback(() => {
    setCurrent(initialPage);
    setPageSize(initialPageSize);
  }, [initialPage, initialPageSize]);
  
  return {
    current,
    pageSize,
    pagination,
    setCurrent,
    setPageSize,
    reset
  };
};

/**
 * 表单重置 Hook
 * @param {object} form - Ant Design 表单实例
 * @param {object} initialValues - 初始值
 * @returns {Function} 重置函数
 */
export const useFormReset = (form, initialValues = {}) => {
  return useCallback(() => {
    form.resetFields();
    form.setFieldsValue(initialValues);
  }, [form, initialValues]);
};

/**
 * 上传进度 Hook
 * @returns {object} 上传相关的状态和方法
 */
export const useUpload = () => {
  const [uploading, setUploading] = useState(false);
  const [progress, setProgress] = useState(0);
  const [uploadedFiles, setUploadedFiles] = useState([]);
  
  const startUpload = useCallback(() => {
    setUploading(true);
    setProgress(0);
  }, []);
  
  const updateProgress = useCallback((percent) => {
    setProgress(percent);
  }, []);
  
  const finishUpload = useCallback((files = []) => {
    setUploading(false);
    setProgress(100);
    setUploadedFiles(files);
  }, []);
  
  const resetUpload = useCallback(() => {
    setUploading(false);
    setProgress(0);
    setUploadedFiles([]);
  }, []);
  
  return {
    uploading,
    progress,
    uploadedFiles,
    startUpload,
    updateProgress,
    finishUpload,
    resetUpload
  };
};

/**
 * 搜索 Hook
 * @param {Function} searchFunction - 搜索函数
 * @param {object} options - 选项
 * @returns {object} 搜索相关的状态和方法
 */
export const useSearch = (searchFunction, options = {}) => {
  const { debounceDelay = 300, immediate = false } = options;
  
  const [query, setQuery] = useState('');
  const [results, setResults] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  
  const debouncedQuery = useDebounce(query, debounceDelay);
  
  const search = useCallback(async (searchQuery) => {
    if (!searchQuery.trim()) {
      setResults([]);
      return;
    }
    
    try {
      setLoading(true);
      setError(null);
      
      const result = await searchFunction(searchQuery);
      setResults(result);
    } catch (err) {
      setError(err);
      setResults([]);
    } finally {
      setLoading(false);
    }
  }, [searchFunction]);
  
  useEffect(() => {
    if (immediate || debouncedQuery) {
      search(debouncedQuery);
    }
  }, [debouncedQuery, search, immediate]);
  
  const clearSearch = useCallback(() => {
    setQuery('');
    setResults([]);
    setError(null);
  }, []);
  
  return {
    query,
    results,
    loading,
    error,
    setQuery,
    search,
    clearSearch
  };
};