import Image from '../models/Image.js';
import { AppError, catchAsync } from '../middleware/errorHandler.js';
import { getFileUrl, deleteFile, deleteProcessedImages } from '../middleware/upload.js';
import path from 'path';
import fs from 'fs/promises';

// 上传单张图片
export const uploadImage = catchAsync(async (req, res, next) => {
  if (!req.file) {
    return next(new AppError('请选择要上传的图片', 400));
  }
  
  const { file, user, body } = req;
  const { purpose = 'general', alt = '', description = '' } = body;
  
  // 构建图片URL映射
  const urls = {};
  if (file.processedImages) {
    Object.entries(file.processedImages).forEach(([size, imageInfo]) => {
      urls[size] = getFileUrl(req, imageInfo.path);
    });
  } else {
    urls.original = getFileUrl(req, file.path);
  }
  
  // 创建图片记录
  const imageData = {
    filename: file.filename,
    originalName: file.originalname,
    path: file.path,
    url: urls.original || getFileUrl(req, file.path),
    urls,
    mimetype: file.mimetype,
    size: file.size,
    uploader: user._id,
    purpose,
    alt,
    description,
    metadata: file.metadata || {},
    processedImages: file.processedImages || null
  };
  
  // 如果有处理过的图片，使用优化后的图片作为主要路径
  if (file.processedImages && file.processedImages.original) {
    imageData.path = file.processedImages.original.path;
    imageData.url = urls.original;
    imageData.size = file.processedImages.original.size;
    imageData.width = file.processedImages.original.width;
    imageData.height = file.processedImages.original.height;
  }
  
  const image = await Image.create(imageData);
  
  // 填充上传者信息
  await image.populate('uploader', 'username displayName avatar');
  
  res.status(201).json({
    success: true,
    message: '图片上传成功',
    data: {
      image
    }
  });
});

// 批量上传图片
export const uploadMultipleImages = catchAsync(async (req, res, next) => {
  if (!req.files || req.files.length === 0) {
    return next(new AppError('请选择要上传的图片', 400));
  }
  
  const { files, user, body } = req;
  const { purpose = 'general', alt = '', description = '' } = body;
  
  const uploadedImages = [];
  
  for (const file of files) {
    // 构建图片URL映射
    const urls = {};
    if (file.processedImages) {
      Object.entries(file.processedImages).forEach(([size, imageInfo]) => {
        urls[size] = getFileUrl(req, imageInfo.path);
      });
    } else {
      urls.original = getFileUrl(req, file.path);
    }
    
    // 创建图片记录
    const imageData = {
      filename: file.filename,
      originalName: file.originalname,
      path: file.path,
      url: urls.original || getFileUrl(req, file.path),
      urls,
      mimetype: file.mimetype,
      size: file.size,
      uploader: user._id,
      purpose,
      alt,
      description,
      metadata: file.metadata || {},
      processedImages: file.processedImages || null
    };
    
    // 如果有处理过的图片，使用优化后的图片作为主要路径
    if (file.processedImages && file.processedImages.original) {
      imageData.path = file.processedImages.original.path;
      imageData.url = urls.original;
      imageData.size = file.processedImages.original.size;
      imageData.width = file.processedImages.original.width;
      imageData.height = file.processedImages.original.height;
    }
    
    const image = await Image.create(imageData);
    await image.populate('uploader', 'username displayName avatar');
    
    uploadedImages.push(image);
  }
  
  res.status(201).json({
    success: true,
    message: `成功上传 ${uploadedImages.length} 张图片`,
    data: {
      images: uploadedImages
    }
  });
});

// 获取图片列表
export const getImages = catchAsync(async (req, res, next) => {
  const {
    page = 1,
    limit = 20,
    sort = '-createdAt',
    purpose,
    status = 'active',
    uploader,
    q // 搜索关键词
  } = req.query;
  
  // 构建查询条件
  const query = {};
  
  // 状态过滤
  if (status && status !== 'all') {
    query.status = status;
  }
  
  // 用途过滤
  if (purpose) {
    query.purpose = purpose;
  }
  
  // 上传者过滤
  if (uploader) {
    query.uploader = uploader;
  }
  
  // 搜索功能
  if (q) {
    query.$or = [
      { originalName: { $regex: q, $options: 'i' } },
      { alt: { $regex: q, $options: 'i' } },
      { description: { $regex: q, $options: 'i' } }
    ];
  }
  
  // 非管理员只能看到自己上传的图片
  if (!req.user || req.user.role !== 'admin') {
    query.uploader = req.user._id;
  }
  
  // 计算分页
  const skip = (parseInt(page) - 1) * parseInt(limit);
  
  // 执行查询
  const [images, total] = await Promise.all([
    Image.find(query)
      .populate('uploader', 'username displayName avatar')
      .sort(sort)
      .skip(skip)
      .limit(parseInt(limit))
      .lean(),
    Image.countDocuments(query)
  ]);
  
  // 计算分页信息
  const totalPages = Math.ceil(total / parseInt(limit));
  const hasNextPage = parseInt(page) < totalPages;
  const hasPrevPage = parseInt(page) > 1;
  
  res.status(200).json({
    success: true,
    data: {
      images,
      pagination: {
        currentPage: parseInt(page),
        totalPages,
        totalItems: total,
        itemsPerPage: parseInt(limit),
        hasNextPage,
        hasPrevPage
      }
    }
  });
});

// 获取单张图片信息
export const getImage = catchAsync(async (req, res, next) => {
  const { id } = req.params;
  
  const image = await Image.findById(id)
    .populate('uploader', 'username displayName avatar')
    .lean();
  
  if (!image) {
    return next(new AppError('图片不存在', 404));
  }
  
  // 检查访问权限
  if (image.status !== 'active') {
    if (!req.user || 
        (req.user._id.toString() !== image.uploader._id.toString() && 
         req.user.role !== 'admin')) {
      return next(new AppError('图片不存在', 404));
    }
  }
  
  res.status(200).json({
    success: true,
    data: {
      image
    }
  });
});

// 更新图片信息
export const updateImage = catchAsync(async (req, res, next) => {
  const { id } = req.params;
  const allowedFields = ['alt', 'description', 'purpose', 'status'];
  
  const image = await Image.findById(id);
  
  if (!image) {
    return next(new AppError('图片不存在', 404));
  }
  
  // 检查权限
  if (req.user._id.toString() !== image.uploader.toString() && 
      req.user.role !== 'admin') {
    return next(new AppError('没有权限修改此图片', 403));
  }
  
  // 过滤允许更新的字段
  const updates = {};
  Object.keys(req.body).forEach(key => {
    if (allowedFields.includes(key)) {
      updates[key] = req.body[key];
    }
  });
  
  // 更新图片信息
  const updatedImage = await Image.findByIdAndUpdate(
    id,
    { ...updates, updatedAt: new Date() },
    { new: true, runValidators: true }
  ).populate('uploader', 'username displayName avatar');
  
  res.status(200).json({
    success: true,
    message: '图片信息更新成功',
    data: {
      image: updatedImage
    }
  });
});

// 删除图片
export const deleteImage = catchAsync(async (req, res, next) => {
  const { id } = req.params;
  
  const image = await Image.findById(id);
  
  if (!image) {
    return next(new AppError('图片不存在', 404));
  }
  
  // 检查权限
  if (req.user._id.toString() !== image.uploader.toString() && 
      req.user.role !== 'admin') {
    return next(new AppError('没有权限删除此图片', 403));
  }
  
  // 检查图片是否被引用
  if (image.references && image.references.length > 0) {
    return next(new AppError('图片正在被使用中，无法删除', 400));
  }
  
  // 删除物理文件
  try {
    // 删除处理过的图片文件
    if (image.processedImages) {
      await deleteProcessedImages(image.processedImages);
    } else {
      // 删除原始文件
      await deleteFile(image.path);
    }
  } catch (error) {
    console.error('删除图片文件失败:', error);
    // 即使文件删除失败，也继续删除数据库记录
  }
  
  // 删除数据库记录
  await Image.findByIdAndDelete(id);
  
  res.status(200).json({
    success: true,
    message: '图片删除成功'
  });
});

// 批量删除图片
export const batchDeleteImages = catchAsync(async (req, res, next) => {
  const { ids } = req.body;
  
  if (!Array.isArray(ids) || ids.length === 0) {
    return next(new AppError('请提供要删除的图片ID列表', 400));
  }
  
  // 查找图片
  const images = await Image.find({ _id: { $in: ids } });
  
  if (images.length === 0) {
    return next(new AppError('没有找到要删除的图片', 404));
  }
  
  // 检查权限
  if (req.user.role !== 'admin') {
    const unauthorizedImages = images.filter(
      image => image.uploader.toString() !== req.user._id.toString()
    );
    
    if (unauthorizedImages.length > 0) {
      return next(new AppError('没有权限删除某些图片', 403));
    }
  }
  
  // 检查是否有图片被引用
  const referencedImages = images.filter(
    image => image.references && image.references.length > 0
  );
  
  if (referencedImages.length > 0) {
    return next(new AppError('部分图片正在被使用中，无法删除', 400));
  }
  
  // 删除物理文件
  const deletePromises = images.map(async (image) => {
    try {
      if (image.processedImages) {
        await deleteProcessedImages(image.processedImages);
      } else {
        await deleteFile(image.path);
      }
    } catch (error) {
      console.error(`删除图片文件失败 ${image._id}:`, error);
    }
  });
  
  await Promise.all(deletePromises);
  
  // 删除数据库记录
  const result = await Image.deleteMany({ _id: { $in: ids } });
  
  res.status(200).json({
    success: true,
    message: `成功删除 ${result.deletedCount} 张图片`,
    data: {
      deletedCount: result.deletedCount
    }
  });
});

// 添加图片引用
export const addImageReference = catchAsync(async (req, res, next) => {
  const { id } = req.params;
  const { referenceType, referenceId } = req.body;
  
  if (!referenceType || !referenceId) {
    return next(new AppError('引用类型和引用ID是必需的', 400));
  }
  
  const image = await Image.findById(id);
  
  if (!image) {
    return next(new AppError('图片不存在', 404));
  }
  
  await image.addReference(referenceType, referenceId);
  
  res.status(200).json({
    success: true,
    message: '图片引用添加成功'
  });
});

// 移除图片引用
export const removeImageReference = catchAsync(async (req, res, next) => {
  const { id } = req.params;
  const { referenceType, referenceId } = req.body;
  
  if (!referenceType || !referenceId) {
    return next(new AppError('引用类型和引用ID是必需的', 400));
  }
  
  const image = await Image.findById(id);
  
  if (!image) {
    return next(new AppError('图片不存在', 404));
  }
  
  await image.removeReference(referenceType, referenceId);
  
  res.status(200).json({
    success: true,
    message: '图片引用移除成功'
  });
});

// 获取图片统计信息
export const getImageStats = catchAsync(async (req, res, next) => {
  const stats = await Image.getStorageStats();
  
  // 获取用途统计
  const purposeStats = await Image.aggregate([
    { $match: { status: 'active' } },
    { $group: { _id: '$purpose', count: { $sum: 1 }, totalSize: { $sum: '$size' } } },
    { $sort: { count: -1 } }
  ]);
  
  // 获取上传者统计（仅管理员可见）
  let uploaderStats = [];
  if (req.user && req.user.role === 'admin') {
    uploaderStats = await Image.aggregate([
      { $match: { status: 'active' } },
      {
        $group: {
          _id: '$uploader',
          count: { $sum: 1 },
          totalSize: { $sum: '$size' }
        }
      },
      {
        $lookup: {
          from: 'users',
          localField: '_id',
          foreignField: '_id',
          as: 'uploader'
        }
      },
      { $unwind: '$uploader' },
      {
        $project: {
          _id: 1,
          count: 1,
          totalSize: 1,
          'uploader.username': 1,
          'uploader.displayName': 1
        }
      },
      { $sort: { count: -1 } },
      { $limit: 10 }
    ]);
  }
  
  res.status(200).json({
    success: true,
    data: {
      overview: stats,
      purposes: purposeStats,
      uploaders: uploaderStats
    }
  });
});

// 清理未使用的图片
export const cleanupUnusedImages = catchAsync(async (req, res, next) => {
  // 只有管理员可以执行清理操作
  if (req.user.role !== 'admin') {
    return next(new AppError('只有管理员可以执行此操作', 403));
  }
  
  const { daysOld = 30 } = req.query;
  const cutoffDate = new Date();
  cutoffDate.setDate(cutoffDate.getDate() - parseInt(daysOld));
  
  // 查找未被引用且创建时间超过指定天数的图片
  const unusedImages = await Image.find({
    status: 'active',
    createdAt: { $lt: cutoffDate },
    $or: [
      { references: { $size: 0 } },
      { references: { $exists: false } }
    ]
  });
  
  if (unusedImages.length === 0) {
    return res.status(200).json({
      success: true,
      message: '没有找到需要清理的图片',
      data: {
        cleanedCount: 0
      }
    });
  }
  
  // 删除物理文件
  const deletePromises = unusedImages.map(async (image) => {
    try {
      if (image.processedImages) {
        await deleteProcessedImages(image.processedImages);
      } else {
        await deleteFile(image.path);
      }
    } catch (error) {
      console.error(`删除图片文件失败 ${image._id}:`, error);
    }
  });
  
  await Promise.all(deletePromises);
  
  // 删除数据库记录
  const result = await Image.deleteMany({
    _id: { $in: unusedImages.map(img => img._id) }
  });
  
  res.status(200).json({
    success: true,
    message: `成功清理 ${result.deletedCount} 张未使用的图片`,
    data: {
      cleanedCount: result.deletedCount
    }
  });
});

// 导出所有图片控制器
export default {
  uploadImage,
  uploadMultipleImages,
  getImages,
  getImage,
  updateImage,
  deleteImage,
  batchDeleteImages,
  addImageReference,
  removeImageReference,
  getImageStats,
  cleanupUnusedImages
};