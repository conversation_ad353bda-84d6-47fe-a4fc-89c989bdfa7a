import express from 'express';
import {
  getUsers,
  getUser,
  createUser,
  updateUser,
  resetUserPassword,
  deleteUser,
  batchUpdateUsers,
  getUsersStats,
  searchUsers,
  getUserActivity,
  toggleUserStatus
} from '../controllers/userController.js';
import {
  authenticate as authenticateToken,
  authorize as requireRole
} from '../middleware/auth.js';
import {
  validateObjectId,
  validatePagination,
  validateSearch,
  validateUserRegistration as validateRegister,
  validateUserUpdate as validateProfileUpdate,
  validatePasswordChange as validatePasswordReset,
  validateBatchOperation as validateBatchUpdate
} from '../middleware/validation.js';

const router = express.Router();

// 所有路由都需要管理员权限
router.use(authenticateToken);
router.use(requireRole(['admin']));

// 获取用户列表
router.get('/', validatePagination, getUsers);

// 搜索用户
router.get('/search', validateSearch, searchUsers);

// 获取用户统计信息
router.get('/stats', getUsersStats);

// 获取单个用户信息
router.get('/:id', validateObjectId('id'), getUser);

// 获取用户活动日志
router.get('/:id/activity', validateObjectId('id'), getUserActivity);

// 创建用户
router.post('/', validateRegister, createUser);

// 更新用户信息
router.put('/:id', validateObjectId('id'), validateProfileUpdate, updateUser);

// 重置用户密码
router.put('/:id/password', validateObjectId('id'), validatePasswordReset, resetUserPassword);

// 切换用户状态
router.patch('/:id/status', validateObjectId('id'), toggleUserStatus);

// 删除用户
router.delete('/:id', validateObjectId('id'), deleteUser);

// 批量操作用户
router.patch('/batch', validateBatchUpdate, batchUpdateUsers);

export default router;