import { body, param, query, validationResult } from 'express-validator';
import mongoose from 'mongoose';

// 处理验证错误
export const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  
  if (!errors.isEmpty()) {
    const formattedErrors = errors.array().map(error => ({
      field: error.path,
      message: error.msg,
      value: error.value
    }));
    
    return res.status(400).json({
      success: false,
      message: '请求数据验证失败',
      errors: formattedErrors
    });
  }
  
  next();
};

// 自定义验证器
const isValidObjectId = (value) => {
  return mongoose.Types.ObjectId.isValid(value);
};

const isValidSlug = (value) => {
  return /^[a-z0-9]+(?:-[a-z0-9]+)*$/.test(value);
};

const isValidPassword = (value) => {
  // 至少8位，包含大小写字母、数字和特殊字符
  return /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/.test(value);
};

const isValidImageType = (mimetype) => {
  const allowedTypes = ['image/jpeg', 'image/png', 'image/webp', 'image/gif'];
  return allowedTypes.includes(mimetype);
};

// 用户相关验证
export const validateUserRegistration = [
  body('username')
    .isLength({ min: 3, max: 30 })
    .withMessage('用户名长度必须在3-30个字符之间')
    .matches(/^[a-zA-Z0-9_]+$/)
    .withMessage('用户名只能包含字母、数字和下划线'),
  
  body('email')
    .isEmail()
    .withMessage('请提供有效的邮箱地址')
    .normalizeEmail(),
  
  body('password')
    .custom(isValidPassword)
    .withMessage('密码必须至少8位，包含大小写字母、数字和特殊字符'),
  
  body('displayName')
    .optional()
    .isLength({ min: 1, max: 50 })
    .withMessage('显示名称长度必须在1-50个字符之间'),
  
  body('bio')
    .optional()
    .isLength({ max: 500 })
    .withMessage('个人简介不能超过500个字符'),
  
  handleValidationErrors
];

export const validateUserLogin = [
  body('identifier')
    .notEmpty()
    .withMessage('用户名或邮箱是必需的'),
  
  body('password')
    .notEmpty()
    .withMessage('密码是必需的'),
  
  handleValidationErrors
];

export const validatePasswordChange = [
  body('currentPassword')
    .notEmpty()
    .withMessage('当前密码是必需的'),
  
  body('newPassword')
    .custom(isValidPassword)
    .withMessage('新密码必须至少8位，包含大小写字母、数字和特殊字符'),
  
  body('confirmPassword')
    .custom((value, { req }) => {
      if (value !== req.body.newPassword) {
        throw new Error('确认密码与新密码不匹配');
      }
      return true;
    }),
  
  handleValidationErrors
];

export const validateUserUpdate = [
  body('displayName')
    .optional()
    .isLength({ min: 1, max: 50 })
    .withMessage('显示名称长度必须在1-50个字符之间'),
  
  body('bio')
    .optional()
    .isLength({ max: 500 })
    .withMessage('个人简介不能超过500个字符'),
  
  body('avatar')
    .optional()
    .isURL()
    .withMessage('头像必须是有效的URL'),
  
  body('website')
    .optional()
    .isURL()
    .withMessage('网站必须是有效的URL'),
  
  body('location')
    .optional()
    .isLength({ max: 100 })
    .withMessage('位置信息不能超过100个字符'),
  
  handleValidationErrors
];

// 文章相关验证
export const validateArticleCreation = [
  body('title')
    .isLength({ min: 1, max: 200 })
    .withMessage('标题长度必须在1-200个字符之间'),
  
  body('content')
    .isLength({ min: 10 })
    .withMessage('内容至少需要10个字符'),
  
  body('slug')
    .optional()
    .custom(isValidSlug)
    .withMessage('URL别名只能包含小写字母、数字和连字符'),
  
  body('excerpt')
    .optional()
    .isLength({ max: 500 })
    .withMessage('摘要不能超过500个字符'),
  
  body('tags')
    .optional()
    .isArray()
    .withMessage('标签必须是数组格式')
    .custom((tags) => {
      if (tags.length > 10) {
        throw new Error('标签数量不能超过10个');
      }
      return tags.every(tag => typeof tag === 'string' && tag.length <= 30);
    })
    .withMessage('每个标签必须是字符串且不超过30个字符'),
  
  body('category')
    .optional()
    .isLength({ min: 1, max: 50 })
    .withMessage('分类长度必须在1-50个字符之间'),
  
  body('coverImage')
    .optional()
    .isURL()
    .withMessage('封面图片必须是有效的URL'),
  
  body('status')
    .optional()
    .isIn(['draft', 'published', 'archived'])
    .withMessage('状态必须是draft、published或archived之一'),
  
  body('featured')
    .optional()
    .isBoolean()
    .withMessage('推荐状态必须是布尔值'),
  
  body('allowComments')
    .optional()
    .isBoolean()
    .withMessage('评论设置必须是布尔值'),
  
  body('publishedAt')
    .optional()
    .isISO8601()
    .withMessage('发布时间必须是有效的日期格式'),
  
  handleValidationErrors
];

export const validateArticleUpdate = [
  body('title')
    .optional()
    .isLength({ min: 1, max: 200 })
    .withMessage('标题长度必须在1-200个字符之间'),
  
  body('content')
    .optional()
    .isLength({ min: 10 })
    .withMessage('内容至少需要10个字符'),
  
  body('slug')
    .optional()
    .custom(isValidSlug)
    .withMessage('URL别名只能包含小写字母、数字和连字符'),
  
  body('excerpt')
    .optional()
    .isLength({ max: 500 })
    .withMessage('摘要不能超过500个字符'),
  
  body('tags')
    .optional()
    .isArray()
    .withMessage('标签必须是数组格式')
    .custom((tags) => {
      if (tags.length > 10) {
        throw new Error('标签数量不能超过10个');
      }
      return tags.every(tag => typeof tag === 'string' && tag.length <= 30);
    })
    .withMessage('每个标签必须是字符串且不超过30个字符'),
  
  body('category')
    .optional()
    .isLength({ min: 1, max: 50 })
    .withMessage('分类长度必须在1-50个字符之间'),
  
  body('coverImage')
    .optional()
    .isURL()
    .withMessage('封面图片必须是有效的URL'),
  
  body('status')
    .optional()
    .isIn(['draft', 'published', 'archived'])
    .withMessage('状态必须是draft、published或archived之一'),
  
  body('featured')
    .optional()
    .isBoolean()
    .withMessage('推荐状态必须是布尔值'),
  
  body('allowComments')
    .optional()
    .isBoolean()
    .withMessage('评论设置必须是布尔值'),
  
  body('publishedAt')
    .optional()
    .isISO8601()
    .withMessage('发布时间必须是有效的日期格式'),
  
  handleValidationErrors
];

// 图片相关验证
export const validateImageUpload = [
  body('purpose')
    .optional()
    .isIn(['article', 'avatar', 'cover', 'general'])
    .withMessage('用途必须是article、avatar、cover或general之一'),
  
  body('alt')
    .optional()
    .isLength({ max: 200 })
    .withMessage('替代文本不能超过200个字符'),
  
  body('description')
    .optional()
    .isLength({ max: 500 })
    .withMessage('描述不能超过500个字符'),
  
  handleValidationErrors
];

// 通用参数验证
export const validateObjectId = (paramName = 'id') => [
  param(paramName)
    .custom(isValidObjectId)
    .withMessage('无效的ID格式'),
  
  handleValidationErrors
];

export const validatePagination = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('页码必须是大于0的整数'),
  
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('每页数量必须是1-100之间的整数'),
  
  query('sort')
    .optional()
    .isIn(['createdAt', '-createdAt', 'updatedAt', '-updatedAt', 'title', '-title', 'views', '-views'])
    .withMessage('排序字段无效'),
  
  handleValidationErrors
];

export const validateSearch = [
  query('q')
    .optional()
    .isLength({ min: 1, max: 100 })
    .withMessage('搜索关键词长度必须在1-100个字符之间'),
  
  query('category')
    .optional()
    .isLength({ min: 1, max: 50 })
    .withMessage('分类长度必须在1-50个字符之间'),
  
  query('tags')
    .optional()
    .custom((value) => {
      if (typeof value === 'string') {
        return value.split(',').every(tag => tag.trim().length <= 30);
      }
      return Array.isArray(value) && value.every(tag => typeof tag === 'string' && tag.length <= 30);
    })
    .withMessage('标签格式无效'),
  
  query('status')
    .optional()
    .isIn(['draft', 'published', 'archived'])
    .withMessage('状态必须是draft、published或archived之一'),
  
  query('featured')
    .optional()
    .isBoolean()
    .withMessage('推荐状态必须是布尔值'),
  
  handleValidationErrors
];

// 刷新令牌验证
export const validateRefreshToken = [
  body('refreshToken')
    .notEmpty()
    .withMessage('刷新令牌是必需的'),
  
  handleValidationErrors
];

// 文件上传验证中间件
export const validateFileUpload = (options = {}) => {
  const {
    maxSize = 5 * 1024 * 1024, // 5MB
    allowedTypes = ['image/jpeg', 'image/png', 'image/webp', 'image/gif'],
    required = true
  } = options;
  
  return (req, res, next) => {
    if (!req.file && required) {
      return res.status(400).json({
        success: false,
        message: '请选择要上传的文件'
      });
    }
    
    if (req.file) {
      // 检查文件大小
      if (req.file.size > maxSize) {
        return res.status(400).json({
          success: false,
          message: `文件大小不能超过 ${Math.round(maxSize / 1024 / 1024)}MB`
        });
      }
      
      // 检查文件类型
      if (!allowedTypes.includes(req.file.mimetype)) {
        return res.status(400).json({
          success: false,
          message: `不支持的文件类型，仅支持: ${allowedTypes.join(', ')}`
        });
      }
    }
    
    next();
  };
};

// 批量操作验证
export const validateBatchOperation = [
  body('ids')
    .isArray({ min: 1, max: 100 })
    .withMessage('ID数组必须包含1-100个元素')
    .custom((ids) => {
      return ids.every(id => isValidObjectId(id));
    })
    .withMessage('所有ID必须是有效的ObjectId格式'),
  
  body('action')
    .isIn(['delete', 'publish', 'unpublish', 'archive', 'feature', 'unfeature'])
    .withMessage('操作类型无效'),
  
  handleValidationErrors
];

// 评论验证（为将来扩展准备）
export const validateComment = [
  body('content')
    .isLength({ min: 1, max: 1000 })
    .withMessage('评论内容长度必须在1-1000个字符之间'),
  
  body('parentId')
    .optional()
    .custom(isValidObjectId)
    .withMessage('父评论ID格式无效'),
  
  handleValidationErrors
];

// 导出所有验证器
export default {
  handleValidationErrors,
  validateUserRegistration,
  validateUserLogin,
  validatePasswordChange,
  validateUserUpdate,
  validateArticleCreation,
  validateArticleUpdate,
  validateImageUpload,
  validateObjectId,
  validatePagination,
  validateSearch,
  validateRefreshToken,
  validateFileUpload,
  validateBatchOperation,
  validateComment
};