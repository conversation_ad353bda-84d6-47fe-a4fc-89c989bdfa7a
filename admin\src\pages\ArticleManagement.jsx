import React, { useState, useRef } from 'react';
import {
  Table,
  Card,
  Button,
  Space,
  Input,
  Select,
  Tag,
  Modal,
  Form,
  message,
  Popconfirm,
  Avatar,
  Tooltip,
  DatePicker,
  Row,
  Col,
  Statistic,
  Divider
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  SearchOutlined,
  ReloadOutlined,
  ExportOutlined,
  FileTextOutlined,
  CalendarOutlined,
  UserOutlined,
  TagOutlined
} from '@ant-design/icons';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useNavigate } from 'react-router-dom';
import { articleAPI } from '@/services/api';
import { formatDate, formatNumber } from '@/utils';
import { useDebounce } from '@/hooks';

const { Search } = Input;
const { Option } = Select;
const { RangePicker } = DatePicker;

const ArticleManagement = () => {
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const [searchForm] = Form.useForm();
  
  // 状态管理
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  });
  
  const [filters, setFilters] = useState({
    keyword: '',
    status: '',
    category: '',
    author: '',
    dateRange: null
  });
  
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [previewVisible, setPreviewVisible] = useState(false);
  const [previewArticle, setPreviewArticle] = useState(null);
  
  // 防抖搜索
  const debouncedKeyword = useDebounce(filters.keyword, 500);
  
  // 获取文章列表
  const {
    data: articleData,
    loading: articleLoading,
    refetch: refetchArticles
  } = useQuery({
    queryKey: ['articles', pagination.current, pagination.pageSize, debouncedKeyword, filters],
    queryFn: () => articleAPI.getList({
      page: pagination.current,
      pageSize: pagination.pageSize,
      keyword: debouncedKeyword,
      status: filters.status,
      category: filters.category,
      author: filters.author,
      startDate: filters.dateRange?.[0]?.format('YYYY-MM-DD'),
      endDate: filters.dateRange?.[1]?.format('YYYY-MM-DD')
    }),
    keepPreviousData: true
  });
  
  // 获取统计数据
  const { data: statsData } = useQuery({
    queryKey: ['article-stats'],
    queryFn: () => articleAPI.getStats()
  });
  
  // 删除文章
  const deleteMutation = useMutation({
    mutationFn: articleAPI.delete,
    onSuccess: () => {
      message.success('删除成功');
      queryClient.invalidateQueries(['articles']);
      queryClient.invalidateQueries(['article-stats']);
      setSelectedRowKeys([]);
    },
    onError: (error) => {
      message.error(error.message || '删除失败');
    }
  });
  
  // 批量删除
  const batchDeleteMutation = useMutation({
    mutationFn: (ids) => articleAPI.batchDelete(ids),
    onSuccess: () => {
      message.success('批量删除成功');
      queryClient.invalidateQueries(['articles']);
      queryClient.invalidateQueries(['article-stats']);
      setSelectedRowKeys([]);
    },
    onError: (error) => {
      message.error(error.message || '批量删除失败');
    }
  });
  
  // 更新文章状态
  const updateStatusMutation = useMutation({
    mutationFn: ({ id, status }) => articleAPI.updateStatus(id, status),
    onSuccess: () => {
      message.success('状态更新成功');
      queryClient.invalidateQueries(['articles']);
      queryClient.invalidateQueries(['article-stats']);
    },
    onError: (error) => {
      message.error(error.message || '状态更新失败');
    }
  });
  
  // 处理搜索
  const handleSearch = (values) => {
    setFilters(prev => ({ ...prev, ...values }));
    setPagination(prev => ({ ...prev, current: 1 }));
  };
  
  // 重置搜索
  const handleReset = () => {
    searchForm.resetFields();
    setFilters({
      keyword: '',
      status: '',
      category: '',
      author: '',
      dateRange: null
    });
    setPagination(prev => ({ ...prev, current: 1 }));
  };
  
  // 处理表格变化
  const handleTableChange = (paginationConfig, filtersConfig, sorter) => {
    setPagination({
      ...pagination,
      current: paginationConfig.current,
      pageSize: paginationConfig.pageSize
    });
  };
  
  // 预览文章
  const handlePreview = (article) => {
    setPreviewArticle(article);
    setPreviewVisible(true);
  };
  
  // 导出数据
  const handleExport = () => {
    // 实现导出逻辑
    message.info('导出功能开发中...');
  };
  
  // 表格列定义
  const columns = [
    {
      title: '文章信息',
      key: 'info',
      width: 300,
      render: (_, record) => (
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <Avatar
            shape="square"
            size={64}
            src={record.coverImage}
            icon={<FileTextOutlined />}
            style={{ marginRight: 12, flexShrink: 0 }}
          />
          <div style={{ flex: 1, minWidth: 0 }}>
            <div
              style={{
                fontWeight: 'bold',
                fontSize: '14px',
                marginBottom: '4px',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap'
              }}
            >
              {record.title}
            </div>
            <div
              style={{
                color: '#666',
                fontSize: '12px',
                marginBottom: '4px',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap'
              }}
            >
              {record.summary || '暂无摘要'}
            </div>
            <Space size={8}>
              <Tag icon={<TagOutlined />} color="blue">
                {record.category}
              </Tag>
              {record.tags?.slice(0, 2).map(tag => (
                <Tag key={tag} size="small">{tag}</Tag>
              ))}
              {record.tags?.length > 2 && (
                <Tag size="small">+{record.tags.length - 2}</Tag>
              )}
            </Space>
          </div>
        </div>
      )
    },
    {
      title: '作者',
      dataIndex: 'author',
      key: 'author',
      width: 120,
      render: (author) => (
        <Space>
          <Avatar size="small" icon={<UserOutlined />} />
          <span>{author?.name || '未知'}</span>
        </Space>
      )
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status) => {
        const statusConfig = {
          published: { color: 'green', text: '已发布' },
          draft: { color: 'orange', text: '草稿' },
          archived: { color: 'red', text: '已归档' }
        };
        const config = statusConfig[status] || { color: 'default', text: status };
        return <Tag color={config.color}>{config.text}</Tag>;
      }
    },
    {
      title: '统计',
      key: 'stats',
      width: 120,
      render: (_, record) => (
        <div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            <EyeOutlined /> {formatNumber(record.views || 0)}
          </div>
          <div style={{ fontSize: '12px', color: '#666', marginTop: '2px' }}>
            💬 {formatNumber(record.comments || 0)}
          </div>
        </div>
      )
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 120,
      render: (date) => (
        <div>
          <div style={{ fontSize: '12px' }}>
            {formatDate(date, 'MM-DD')}
          </div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            {formatDate(date, 'HH:mm')}
          </div>
        </div>
      )
    },
    {
      title: '操作',
      key: 'actions',
      width: 200,
      fixed: 'right',
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="预览">
            <Button
              type="text"
              size="small"
              icon={<EyeOutlined />}
              onClick={() => handlePreview(record)}
            />
          </Tooltip>
          <Tooltip title="编辑">
            <Button
              type="text"
              size="small"
              icon={<EditOutlined />}
              onClick={() => navigate(`/articles/edit/${record.id}`)}
            />
          </Tooltip>
          <Select
            size="small"
            value={record.status}
            style={{ width: 80 }}
            onChange={(status) => updateStatusMutation.mutate({ id: record.id, status })}
          >
            <Option value="published">发布</Option>
            <Option value="draft">草稿</Option>
            <Option value="archived">归档</Option>
          </Select>
          <Popconfirm
            title="确定要删除这篇文章吗？"
            onConfirm={() => deleteMutation.mutate(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="text"
              size="small"
              danger
              icon={<DeleteOutlined />}
              loading={deleteMutation.isLoading}
            />
          </Popconfirm>
        </Space>
      )
    }
  ];
  
  // 行选择配置
  const rowSelection = {
    selectedRowKeys,
    onChange: setSelectedRowKeys,
    getCheckboxProps: (record) => ({
      disabled: record.status === 'archived'
    })
  };
  
  return (
    <div>
      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="总文章数"
              value={statsData?.total || 0}
              prefix={<FileTextOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="已发布"
              value={statsData?.published || 0}
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="草稿"
              value={statsData?.draft || 0}
              valueStyle={{ color: '#cf1322' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="总阅读量"
              value={statsData?.totalViews || 0}
              prefix={<EyeOutlined />}
            />
          </Card>
        </Col>
      </Row>
      
      <Card>
        {/* 搜索表单 */}
        <Form
          form={searchForm}
          layout="inline"
          onFinish={handleSearch}
          style={{ marginBottom: 16 }}
        >
          <Form.Item name="keyword">
            <Search
              placeholder="搜索标题、内容..."
              style={{ width: 200 }}
              onChange={(e) => setFilters(prev => ({ ...prev, keyword: e.target.value }))}
              allowClear
            />
          </Form.Item>
          <Form.Item name="status">
            <Select placeholder="状态" style={{ width: 120 }} allowClear>
              <Option value="published">已发布</Option>
              <Option value="draft">草稿</Option>
              <Option value="archived">已归档</Option>
            </Select>
          </Form.Item>
          <Form.Item name="category">
            <Select placeholder="分类" style={{ width: 120 }} allowClear>
              <Option value="tech">技术</Option>
              <Option value="life">生活</Option>
              <Option value="travel">旅行</Option>
            </Select>
          </Form.Item>
          <Form.Item name="dateRange">
            <RangePicker placeholder={['开始日期', '结束日期']} />
          </Form.Item>
          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit" icon={<SearchOutlined />}>
                搜索
              </Button>
              <Button onClick={handleReset}>
                重置
              </Button>
            </Space>
          </Form.Item>
        </Form>
        
        <Divider />
        
        {/* 操作按钮 */}
        <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between' }}>
          <Space>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => navigate('/articles/create')}
            >
              新建文章
            </Button>
            {selectedRowKeys.length > 0 && (
              <Popconfirm
                title={`确定要删除选中的 ${selectedRowKeys.length} 篇文章吗？`}
                onConfirm={() => batchDeleteMutation.mutate(selectedRowKeys)}
                okText="确定"
                cancelText="取消"
              >
                <Button
                  danger
                  icon={<DeleteOutlined />}
                  loading={batchDeleteMutation.isLoading}
                >
                  批量删除 ({selectedRowKeys.length})
                </Button>
              </Popconfirm>
            )}
          </Space>
          <Space>
            <Button icon={<ExportOutlined />} onClick={handleExport}>
              导出
            </Button>
            <Button icon={<ReloadOutlined />} onClick={() => refetchArticles()}>
              刷新
            </Button>
          </Space>
        </div>
        
        {/* 文章表格 */}
        <Table
          rowSelection={rowSelection}
          columns={columns}
          dataSource={articleData?.data || []}
          loading={articleLoading}
          rowKey="id"
          pagination={{
            ...pagination,
            total: articleData?.total || 0,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
          }}
          onChange={handleTableChange}
          scroll={{ x: 1200 }}
        />
      </Card>
      
      {/* 预览模态框 */}
      <Modal
        title="文章预览"
        open={previewVisible}
        onCancel={() => setPreviewVisible(false)}
        footer={[
          <Button key="close" onClick={() => setPreviewVisible(false)}>
            关闭
          </Button>,
          <Button
            key="edit"
            type="primary"
            onClick={() => {
              setPreviewVisible(false);
              navigate(`/articles/edit/${previewArticle?.id}`);
            }}
          >
            编辑文章
          </Button>
        ]}
        width={800}
      >
        {previewArticle && (
          <div>
            <h2>{previewArticle.title}</h2>
            <div style={{ marginBottom: 16, color: '#666' }}>
              <Space>
                <span>作者：{previewArticle.author?.name}</span>
                <span>发布时间：{formatDate(previewArticle.createdAt)}</span>
                <span>阅读量：{formatNumber(previewArticle.views)}</span>
              </Space>
            </div>
            {previewArticle.coverImage && (
              <img
                src={previewArticle.coverImage}
                alt="封面"
                style={{ width: '100%', maxHeight: 200, objectFit: 'cover', marginBottom: 16 }}
              />
            )}
            <div
              style={{ lineHeight: 1.6 }}
              dangerouslySetInnerHTML={{ __html: previewArticle.content }}
            />
          </div>
        )}
      </Modal>
    </div>
  );
};

export default ArticleManagement;