# 数据同步实现方案

## 目标
实现后端管理界面与前端展示数据的完全同步，确保所有数据变更能够实时双向更新。

## 技术方案

### 1. WebSocket 实时通信
- 使用 Socket.IO 实现服务端和客户端的实时通信
- 支持房间机制，区分管理端和展示端
- 实现断线重连和错误处理

### 2. 数据同步事件
- 文章数据变更（增删改查）
- 图片数据变更
- 用户数据变更
- 系统配置变更

### 3. 缓存失效策略
- React Query 缓存自动失效
- 前端 Pinia store 状态更新
- 浏览器缓存控制

### 4. 实现步骤

#### 步骤1: 后端 WebSocket 服务
1. 安装 socket.io 依赖
2. 创建 WebSocket 服务器
3. 定义事件类型和数据格式
4. 集成到现有 Express 应用

#### 步骤2: 数据变更监听
1. 在文章控制器中添加事件发送
2. 在图片控制器中添加事件发送
3. 创建统一的事件发送服务

#### 步骤3: 管理端 WebSocket 客户端
1. 安装 socket.io-client
2. 创建 WebSocket 连接服务
3. 集成 React Query 缓存失效
4. 添加连接状态管理

#### 步骤4: 前端展示端 WebSocket 客户端
1. 在 Vue 应用中集成 socket.io-client
2. 创建 Pinia store 用于 WebSocket 状态
3. 实现数据自动刷新机制

#### 步骤5: 测试和优化
1. 端到端测试数据同步
2. 性能优化和错误处理
3. 添加日志和监控

## 事件定义

### 文章相关事件
- `article:created` - 文章创建
- `article:updated` - 文章更新
- `article:deleted` - 文章删除
- `article:published` - 文章发布
- `article:unpublished` - 文章取消发布

### 图片相关事件
- `image:uploaded` - 图片上传
- `image:updated` - 图片信息更新
- `image:deleted` - 图片删除

### 系统事件
- `system:cache_clear` - 清除缓存
- `system:config_updated` - 配置更新

## 数据格式

```javascript
{
  type: 'article:updated',
  data: {
    id: 'article_id',
    changes: { title: 'new title' },
    timestamp: '2024-01-01T00:00:00Z'
  },
  source: 'admin' // 或 'frontend'
}
```

## 房间管理
- `admin` - 管理端房间
- `frontend` - 前端展示房间
- `all` - 广播到所有客户端

## 错误处理
- 连接失败重试机制
- 数据同步失败回滚
- 离线状态处理
- 冲突解决策略