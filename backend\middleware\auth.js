import jwtConfig from '../config/jwt.js';
import User from '../models/User.js';

// 验证JWT令牌
export const authenticate = async (req, res, next) => {
  try {
    const authHeader = req.header('Authorization');
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        success: false,
        message: '访问被拒绝，请提供有效的令牌'
      });
    }
    
    const token = authHeader.substring(7); // 移除 'Bearer ' 前缀
    
    if (!token) {
      return res.status(401).json({
        success: false,
        message: '访问被拒绝，令牌不存在'
      });
    }
    
    // 验证令牌
    const decoded = jwtConfig.verifyToken(token);
    
    // 查找用户
    const user = await User.findById(decoded.userId).select('-refreshTokens');
    if (!user) {
      return res.status(401).json({
        success: false,
        message: '令牌无效，用户不存在'
      });
    }
    
    // 检查用户状态
    if (user.status !== 'active') {
      return res.status(401).json({
        success: false,
        message: '用户账户已被禁用'
      });
    }
    
    // 将用户信息添加到请求对象
    req.user = user;
    req.token = token;
    
    next();
  } catch (error) {
    console.error('认证错误:', error.message);
    
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({
        success: false,
        message: '令牌无效'
      });
    }
    
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({
        success: false,
        message: '令牌已过期'
      });
    }
    
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
};

// 可选认证（不强制要求登录）
export const optionalAuth = async (req, res, next) => {
  try {
    const authHeader = req.header('Authorization');
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return next(); // 没有令牌，继续执行
    }
    
    const token = authHeader.substring(7);
    
    if (!token) {
      return next(); // 没有令牌，继续执行
    }
    
    // 尝试验证令牌
    const decoded = jwtConfig.verifyToken(token);
    const user = await User.findById(decoded.userId).select('-refreshTokens');
    
    if (user && user.status === 'active') {
      req.user = user;
      req.token = token;
    }
    
    next();
  } catch (error) {
    // 令牌无效或过期，但不阻止请求继续
    next();
  }
};

// 角色权限检查
export const authorize = (...roles) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: '访问被拒绝，请先登录'
      });
    }
    
    if (!roles.includes(req.user.role)) {
      return res.status(403).json({
        success: false,
        message: '访问被拒绝，权限不足'
      });
    }
    
    next();
  };
};

// 检查是否为管理员
export const requireAdmin = authorize('admin');

// 检查是否为编辑者或管理员
export const requireEditor = authorize('admin', 'editor');

// 检查资源所有权（用户只能操作自己的资源）
export const checkOwnership = (resourceModel, resourceIdParam = 'id') => {
  return async (req, res, next) => {
    try {
      if (!req.user) {
        return res.status(401).json({
          success: false,
          message: '访问被拒绝，请先登录'
        });
      }
      
      // 管理员可以访问所有资源
      if (req.user.role === 'admin') {
        return next();
      }
      
      const resourceId = req.params[resourceIdParam];
      const resource = await resourceModel.findById(resourceId);
      
      if (!resource) {
        return res.status(404).json({
          success: false,
          message: '资源不存在'
        });
      }
      
      // 检查资源所有权
      const ownerId = resource.author || resource.uploader || resource.user;
      if (!ownerId || ownerId.toString() !== req.user._id.toString()) {
        return res.status(403).json({
          success: false,
          message: '访问被拒绝，您只能操作自己的资源'
        });
      }
      
      req.resource = resource;
      next();
    } catch (error) {
      console.error('所有权检查错误:', error);
      res.status(500).json({
        success: false,
        message: '服务器内部错误'
      });
    }
  };
};

// 刷新令牌验证
export const verifyRefreshToken = async (req, res, next) => {
  try {
    const { refreshToken } = req.body;
    
    if (!refreshToken) {
      return res.status(401).json({
        success: false,
        message: '刷新令牌是必需的'
      });
    }
    
    // 验证刷新令牌
    const decoded = jwtConfig.verifyToken(refreshToken);
    
    // 查找用户并检查刷新令牌是否存在
    const user = await User.findById(decoded.userId).select('+refreshTokens');
    if (!user) {
      return res.status(401).json({
        success: false,
        message: '用户不存在'
      });
    }
    
    const tokenExists = user.refreshTokens.some(rt => rt.token === refreshToken);
    if (!tokenExists) {
      return res.status(401).json({
        success: false,
        message: '刷新令牌无效'
      });
    }
    
    // 检查用户状态
    if (user.status !== 'active') {
      return res.status(401).json({
        success: false,
        message: '用户账户已被禁用'
      });
    }
    
    req.user = user;
    req.refreshToken = refreshToken;
    
    next();
  } catch (error) {
    console.error('刷新令牌验证错误:', error.message);
    
    if (error.name === 'JsonWebTokenError' || error.name === 'TokenExpiredError') {
      return res.status(401).json({
        success: false,
        message: '刷新令牌无效或已过期'
      });
    }
    
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
};

// 限制登录尝试
export const loginAttemptLimiter = (() => {
  const attempts = new Map();
  const MAX_ATTEMPTS = 5;
  const WINDOW_MS = 15 * 60 * 1000; // 15分钟
  const BLOCK_DURATION = 30 * 60 * 1000; // 30分钟
  
  return (req, res, next) => {
    const ip = req.ip || req.connection.remoteAddress;
    const now = Date.now();
    
    if (!attempts.has(ip)) {
      attempts.set(ip, { count: 0, firstAttempt: now, blockedUntil: null });
    }
    
    const record = attempts.get(ip);
    
    // 检查是否仍在封锁期
    if (record.blockedUntil && now < record.blockedUntil) {
      const remainingTime = Math.ceil((record.blockedUntil - now) / 1000 / 60);
      return res.status(429).json({
        success: false,
        message: `登录尝试过于频繁，请在 ${remainingTime} 分钟后重试`
      });
    }
    
    // 重置过期的记录
    if (now - record.firstAttempt > WINDOW_MS) {
      record.count = 0;
      record.firstAttempt = now;
      record.blockedUntil = null;
    }
    
    // 检查是否超过最大尝试次数
    if (record.count >= MAX_ATTEMPTS) {
      record.blockedUntil = now + BLOCK_DURATION;
      return res.status(429).json({
        success: false,
        message: `登录尝试次数过多，账户已被临时锁定 30 分钟`
      });
    }
    
    // 记录失败的登录尝试
    req.recordFailedLogin = () => {
      record.count++;
      attempts.set(ip, record);
    };
    
    // 清除成功登录的记录
    req.clearLoginAttempts = () => {
      attempts.delete(ip);
    };
    
    next();
  };
})();