import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import rateLimit from 'express-rate-limit';
import morgan from 'morgan';
import path from 'path';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';

// 导入配置和中间件
// import DatabaseConfig from './config/database.js'; // 临时注释掉数据库连接
import apiRoutes from './routes/index.js';
import { globalErrorHandler, notFound } from './middleware/errorHandler.js';
import { requestLogger, performanceMonitor, startSystemMonitoring, cleanupOldLogs } from './middleware/monitoring.js';

// 获取当前文件目录
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 加载环境变量
dotenv.config();

// 创建Express应用
const app = express();

// 信任代理（用于部署在反向代理后）
app.set('trust proxy', 1);

// 安全中间件
app.use(helmet({
  crossOriginResourcePolicy: { policy: 'cross-origin' },
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", 'data:', 'https:'],
      connectSrc: ["'self'"],
      fontSrc: ["'self'"],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'"],
      frameSrc: ["'none'"]
    }
  }
}));

// CORS配置
const corsOptions = {
  origin: function (origin, callback) {
    // 允许的域名列表
    const allowedOrigins = process.env.CORS_ORIGIN 
      ? process.env.CORS_ORIGIN.split(',').map(url => url.trim())
      : [
          'http://localhost:3000',
          'http://localhost:5173',
          'http://127.0.0.1:3000',
          'http://127.0.0.1:5173'
        ];
    
    // 开发环境允许所有来源
    if (process.env.NODE_ENV === 'development') {
      return callback(null, true);
    }
    
    // 生产环境检查来源
    if (!origin || allowedOrigins.includes(origin)) {
      callback(null, true);
    } else {
      callback(new Error('不被CORS策略允许的来源'));
    }
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
  exposedHeaders: ['X-Total-Count', 'X-Page-Count']
};

app.use(cors(corsOptions));

// 压缩响应
app.use(compression());

// 日志中间件
if (process.env.NODE_ENV === 'development') {
  app.use(morgan('dev'));
} else {
  app.use(morgan('combined'));
}

// 自定义监控中间件
app.use(requestLogger);
app.use(performanceMonitor);

// 速率限制
const limiter = rateLimit({
  windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 15 * 60 * 1000, // 15分钟
  max: process.env.NODE_ENV === 'development' 
    ? 1000 
    : parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100,
  message: {
    success: false,
    message: '请求过于频繁，请稍后再试',
    retryAfter: Math.ceil((parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 15 * 60 * 1000) / 60000) + '分钟'
  },
  standardHeaders: true,
  legacyHeaders: false,
  skip: (req) => {
    // 跳过健康检查和静态资源
    return req.path === '/api/health' || req.path.startsWith('/uploads/');
  }
});

app.use('/api/', limiter);

// 认证相关的更严格限制
const authLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: parseInt(process.env.RATE_LIMIT_AUTH_MAX) || 5, // 登录/注册限制更严格
  message: {
    success: false,
    message: '认证请求过于频繁，请15分钟后再试',
    code: 'TOO_MANY_AUTH_ATTEMPTS'
  },
  skipSuccessfulRequests: true,
  keyGenerator: (req) => {
    // 基于IP和用户标识生成key
    return `auth_${req.ip}_${req.body.email || req.body.username || 'unknown'}`;
  }
});

app.use('/api/auth/login', authLimiter);
app.use('/api/auth/register', authLimiter);

// 解析请求体
app.use(express.json({ 
  limit: '10mb',
  verify: (req, res, buf) => {
    try {
      JSON.parse(buf);
    } catch (e) {
      res.status(400).json({
        success: false,
        message: '无效的JSON格式'
      });
      return;
    }
  }
}));

app.use(express.urlencoded({ 
  extended: true, 
  limit: '10mb' 
}));

// 静态文件服务
app.use('/uploads', express.static(path.join(__dirname, 'uploads'), {
  maxAge: '1d',
  etag: true,
  lastModified: true
}));

// API路由
app.use('/api', apiRoutes);

// 根路径
app.get('/', (req, res) => {
  res.json({
    success: true,
    message: '欢迎使用墨影博客API',
    version: '1.0.0',
    documentation: {
      api: '/api',
      health: '/api/health'
    },
    author: 'MoYing Blog Team',
    timestamp: new Date().toISOString()
  });
});

// 404处理中间件
app.use(notFound);

// 全局错误处理中间件
app.use(globalErrorHandler);

// 数据库连接
// const dbConfig = new DatabaseConfig(); // 临时注释掉数据库连接

// 优雅关闭处理
const gracefulShutdown = async (signal) => {
  console.log(`\n收到 ${signal} 信号，开始优雅关闭...`);
  
  try {
    // 关闭数据库连接
    // await dbConfig.disconnect(); // 临时注释掉数据库断开连接
    // console.log('数据库连接已关闭');
    
    // 关闭服务器
    if (app.server) {
      app.server.close(() => {
        console.log('HTTP服务器已关闭');
        process.exit(0);
      });
    } else {
      process.exit(0);
    }
  } catch (error) {
    console.error('优雅关闭过程中发生错误:', error);
    process.exit(1);
  }
};

// 监听关闭信号
process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));

// 未捕获的异常处理
process.on('uncaughtException', (error) => {
  console.error('未捕获的异常:', error);
  gracefulShutdown('uncaughtException');
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('未处理的Promise拒绝:', reason);
  gracefulShutdown('unhandledRejection');
});

// 启动系统监控
if (process.env.NODE_ENV === 'production') {
  startSystemMonitoring(60000); // 每分钟检查一次
  
  // 每天清理一次旧日志
  setInterval(() => {
    cleanupOldLogs(30); // 保留30天的日志
  }, 24 * 60 * 60 * 1000);
}

export default app;