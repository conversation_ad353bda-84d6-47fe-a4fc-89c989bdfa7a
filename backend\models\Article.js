import mongoose from 'mongoose';

const articleSchema = new mongoose.Schema({
  slug: {
    type: String,
    required: [true, '文章标识符是必需的'],
    unique: true,
    trim: true,
    lowercase: true,
    match: [/^[a-z0-9-]+$/, '标识符只能包含小写字母、数字和连字符']
  },
  title: {
    type: String,
    required: [true, '文章标题是必需的'],
    trim: true,
    maxlength: [200, '标题不能超过200个字符']
  },
  description: {
    type: String,
    required: [true, '文章描述是必需的'],
    trim: true,
    maxlength: [500, '描述不能超过500个字符']
  },
  content: {
    type: String,
    required: [true, '文章内容是必需的']
  },
  excerpt: {
    type: String,
    trim: true,
    maxlength: [300, '摘要不能超过300个字符']
  },
  coverImage: {
    type: String,
    default: null
  },
  images: [{
    url: String,
    alt: String,
    caption: String
  }],
  tags: [{
    type: String,
    trim: true,
    maxlength: [30, '标签不能超过30个字符']
  }],
  categories: [{
    type: String,
    trim: true,
    maxlength: [50, '分类不能超过50个字符']
  }],
  author: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, '作者是必需的']
  },
  status: {
    type: String,
    enum: ['draft', 'published', 'archived'],
    default: 'draft'
  },
  featured: {
    type: Boolean,
    default: false
  },
  publishedAt: {
    type: Date,
    default: null
  },
  scheduledAt: {
    type: Date,
    default: null
  },
  metadata: {
    readingTime: {
      type: Number,
      default: 0 // 预计阅读时间（分钟）
    },
    wordCount: {
      type: Number,
      default: 0
    },
    views: {
      type: Number,
      default: 0
    },
    likes: {
      type: Number,
      default: 0
    },
    shares: {
      type: Number,
      default: 0
    }
  },
  seo: {
    metaTitle: {
      type: String,
      trim: true,
      maxlength: [60, 'SEO标题不能超过60个字符']
    },
    metaDescription: {
      type: String,
      trim: true,
      maxlength: [160, 'SEO描述不能超过160个字符']
    },
    keywords: [{
      type: String,
      trim: true
    }],
    canonicalUrl: {
      type: String,
      trim: true
    }
  },
  settings: {
    allowComments: {
      type: Boolean,
      default: true
    },
    showInFeed: {
      type: Boolean,
      default: true
    },
    showInSitemap: {
      type: Boolean,
      default: true
    }
  }
}, {
  timestamps: true,
  toJSON: {
    transform: function(doc, ret) {
      delete ret.__v;
      return ret;
    }
  }
});

// 索引
articleSchema.index({ slug: 1 });
articleSchema.index({ status: 1, publishedAt: -1 });
articleSchema.index({ author: 1, createdAt: -1 });
articleSchema.index({ tags: 1 });
articleSchema.index({ categories: 1 });
articleSchema.index({ featured: 1, publishedAt: -1 });
articleSchema.index({ 'metadata.views': -1 });
articleSchema.index({ 'metadata.likes': -1 });

// 文本搜索索引
articleSchema.index({
  title: 'text',
  description: 'text',
  content: 'text',
  tags: 'text'
});

// 虚拟字段：格式化的发布日期
articleSchema.virtual('formattedPublishedAt').get(function() {
  if (!this.publishedAt) return null;
  return this.publishedAt.toISOString().split('T')[0];
});

// 虚拟字段：是否已发布
articleSchema.virtual('isPublished').get(function() {
  return this.status === 'published' && this.publishedAt && this.publishedAt <= new Date();
});

// 虚拟字段：是否为定时发布
articleSchema.virtual('isScheduled').get(function() {
  return this.status === 'published' && this.publishedAt && this.publishedAt > new Date();
});

// 中间件：保存前处理
articleSchema.pre('save', function(next) {
  // 自动生成摘要
  if (!this.excerpt && this.content) {
    const plainText = this.content.replace(/<[^>]*>/g, '').replace(/[#*`]/g, '');
    this.excerpt = plainText.substring(0, 200) + (plainText.length > 200 ? '...' : '');
  }
  
  // 计算字数和阅读时间
  if (this.content) {
    const plainText = this.content.replace(/<[^>]*>/g, '').replace(/[#*`]/g, '');
    this.metadata.wordCount = plainText.length;
    this.metadata.readingTime = Math.ceil(plainText.length / 500); // 假设每分钟阅读500字
  }
  
  // 设置发布时间
  if (this.status === 'published' && !this.publishedAt) {
    this.publishedAt = new Date();
  }
  
  // 自动生成SEO元数据
  if (!this.seo.metaTitle) {
    this.seo.metaTitle = this.title;
  }
  if (!this.seo.metaDescription) {
    this.seo.metaDescription = this.description;
  }
  
  next();
});

// 静态方法：获取已发布的文章
articleSchema.statics.getPublished = function(options = {}) {
  const {
    page = 1,
    limit = 10,
    category,
    tag,
    author,
    featured,
    search
  } = options;
  
  const query = {
    status: 'published',
    publishedAt: { $lte: new Date() }
  };
  
  if (category) query.categories = category;
  if (tag) query.tags = tag;
  if (author) query.author = author;
  if (featured !== undefined) query.featured = featured;
  if (search) {
    query.$text = { $search: search };
  }
  
  const skip = (page - 1) * limit;
  
  return this.find(query)
    .populate('author', 'username profile.displayName profile.avatar')
    .sort({ publishedAt: -1 })
    .skip(skip)
    .limit(limit);
};

// 静态方法：获取热门文章
articleSchema.statics.getPopular = function(limit = 5) {
  return this.find({
    status: 'published',
    publishedAt: { $lte: new Date() }
  })
  .populate('author', 'username profile.displayName')
  .sort({ 'metadata.views': -1, 'metadata.likes': -1 })
  .limit(limit);
};

// 静态方法：获取相关文章
articleSchema.statics.getRelated = function(articleId, tags, limit = 3) {
  return this.find({
    _id: { $ne: articleId },
    status: 'published',
    publishedAt: { $lte: new Date() },
    tags: { $in: tags }
  })
  .populate('author', 'username profile.displayName')
  .sort({ publishedAt: -1 })
  .limit(limit);
};

// 实例方法：增加浏览量
articleSchema.methods.incrementViews = function() {
  this.metadata.views += 1;
  return this.save();
};

// 实例方法：切换点赞
articleSchema.methods.toggleLike = function() {
  this.metadata.likes += 1;
  return this.save();
};

const Article = mongoose.model('Article', articleSchema);

export default Article;