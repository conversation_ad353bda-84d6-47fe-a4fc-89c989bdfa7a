import express from 'express';
import { tempLogin } from '../controllers/tempAuthController.js';
import { validateUserLogin } from '../middleware/validation.js';

const router = express.Router();

// 临时登录路由（用于测试）
router.post('/login', validateUserLogin, tempLogin);

// 测试路由
router.get('/test', (req, res) => {
  res.json({
    success: true,
    message: '临时认证路由工作正常',
    timestamp: new Date().toISOString()
  });
});

export default router;