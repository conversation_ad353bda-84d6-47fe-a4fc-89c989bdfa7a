import React, { useState, useRef } from 'react';
import {
  Card,
  Upload,
  Button,
  Space,
  Input,
  Select,
  Modal,
  message,
  Popconfirm,
  Image,
  Tag,
  Tooltip,
  Row,
  Col,
  Statistic,
  Progress,
  List,
  Avatar,
  Checkbox,
  Divider,
  Form,
  DatePicker
} from 'antd';
import {
  PlusOutlined,
  DeleteOutlined,
  EyeOutlined,
  DownloadOutlined,
  CopyOutlined,
  SearchOutlined,
  ReloadOutlined,
  FolderOutlined,
  FileImageOutlined,
  CloudUploadOutlined,
  LinkOutlined
} from '@ant-design/icons';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { imageAPI } from '@/services/api';
import { formatDate, formatFileSize, copyToClipboard } from '@/utils';
import { useDebounce } from '@/hooks';

const { Search } = Input;
const { Option } = Select;
const { RangePicker } = DatePicker;

const ImageManagement = () => {
  const queryClient = useQueryClient();
  const [searchForm] = Form.useForm();
  const uploadRef = useRef();
  
  // 状态管理
  const [viewMode, setViewMode] = useState('grid'); // grid | list
  const [selectedImages, setSelectedImages] = useState([]);
  const [previewVisible, setPreviewVisible] = useState(false);
  const [previewImage, setPreviewImage] = useState('');
  const [uploadVisible, setUploadVisible] = useState(false);
  const [currentFolder, setCurrentFolder] = useState('/');
  
  const [filters, setFilters] = useState({
    keyword: '',
    type: '',
    folder: '',
    dateRange: null
  });
  
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0
  });
  
  // 防抖搜索
  const debouncedKeyword = useDebounce(filters.keyword, 500);
  
  // 获取图片列表
  const {
    data: imageData,
    loading: imageLoading,
    refetch: refetchImages
  } = useQuery({
    queryKey: ['images', pagination.current, pagination.pageSize, debouncedKeyword, filters, currentFolder],
    queryFn: () => imageAPI.getList({
      page: pagination.current,
      pageSize: pagination.pageSize,
      keyword: debouncedKeyword,
      type: filters.type,
      folder: currentFolder,
      startDate: filters.dateRange?.[0]?.format('YYYY-MM-DD'),
      endDate: filters.dateRange?.[1]?.format('YYYY-MM-DD')
    }),
    keepPreviousData: true
  });
  
  // 获取统计数据
  const { data: statsData } = useQuery({
    queryKey: ['image-stats'],
    queryFn: () => imageAPI.getStats()
  });
  
  // 获取文件夹列表
  const { data: foldersData } = useQuery({
    queryKey: ['image-folders'],
    queryFn: () => imageAPI.getFolders()
  });
  
  // 上传图片
  const uploadMutation = useMutation({
    mutationFn: imageAPI.upload,
    onSuccess: () => {
      message.success('上传成功');
      queryClient.invalidateQueries(['images']);
      queryClient.invalidateQueries(['image-stats']);
      setUploadVisible(false);
    },
    onError: (error) => {
      message.error(error.message || '上传失败');
    }
  });
  
  // 删除图片
  const deleteMutation = useMutation({
    mutationFn: imageAPI.delete,
    onSuccess: () => {
      message.success('删除成功');
      queryClient.invalidateQueries(['images']);
      queryClient.invalidateQueries(['image-stats']);
      setSelectedImages([]);
    },
    onError: (error) => {
      message.error(error.message || '删除失败');
    }
  });
  
  // 批量删除
  const batchDeleteMutation = useMutation({
    mutationFn: (ids) => imageAPI.batchDelete(ids),
    onSuccess: () => {
      message.success('批量删除成功');
      queryClient.invalidateQueries(['images']);
      queryClient.invalidateQueries(['image-stats']);
      setSelectedImages([]);
    },
    onError: (error) => {
      message.error(error.message || '批量删除失败');
    }
  });
  
  // 处理搜索
  const handleSearch = (values) => {
    setFilters(prev => ({ ...prev, ...values }));
    setPagination(prev => ({ ...prev, current: 1 }));
  };
  
  // 重置搜索
  const handleReset = () => {
    searchForm.resetFields();
    setFilters({
      keyword: '',
      type: '',
      folder: '',
      dateRange: null
    });
    setPagination(prev => ({ ...prev, current: 1 }));
  };
  
  // 复制图片链接
  const handleCopyLink = (url) => {
    copyToClipboard(url);
    message.success('链接已复制到剪贴板');
  };
  
  // 下载图片
  const handleDownload = (url, filename) => {
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };
  
  // 上传配置
  const uploadProps = {
    name: 'file',
    multiple: true,
    accept: 'image/*',
    showUploadList: false,
    customRequest: ({ file, onSuccess, onError }) => {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('folder', currentFolder);
      
      uploadMutation.mutate(formData, {
        onSuccess: () => onSuccess(),
        onError: (error) => onError(error)
      });
    },
    beforeUpload: (file) => {
      const isImage = file.type.startsWith('image/');
      if (!isImage) {
        message.error('只能上传图片文件!');
        return false;
      }
      const isLt10M = file.size / 1024 / 1024 < 10;
      if (!isLt10M) {
        message.error('图片大小不能超过 10MB!');
        return false;
      }
      return true;
    }
  };
  
  // 网格视图渲染
  const renderGridView = () => (
    <Row gutter={[16, 16]}>
      {imageData?.data?.map((image) => (
        <Col key={image.id} xs={12} sm={8} md={6} lg={4} xl={3}>
          <Card
            hoverable
            style={{
              position: 'relative',
              border: selectedImages.includes(image.id) ? '2px solid #1890ff' : '1px solid #d9d9d9'
            }}
            cover={
              <div style={{ position: 'relative', paddingTop: '75%', overflow: 'hidden' }}>
                <img
                  src={image.thumbnailUrl || image.url}
                  alt={image.name}
                  style={{
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    width: '100%',
                    height: '100%',
                    objectFit: 'cover',
                    cursor: 'pointer'
                  }}
                  onClick={() => {
                    setPreviewImage(image.url);
                    setPreviewVisible(true);
                  }}
                />
                <Checkbox
                  style={{
                    position: 'absolute',
                    top: 8,
                    left: 8,
                    zIndex: 1
                  }}
                  checked={selectedImages.includes(image.id)}
                  onChange={(e) => {
                    if (e.target.checked) {
                      setSelectedImages([...selectedImages, image.id]);
                    } else {
                      setSelectedImages(selectedImages.filter(id => id !== image.id));
                    }
                  }}
                />
              </div>
            }
            actions={[
              <Tooltip title="预览">
                <EyeOutlined
                  onClick={() => {
                    setPreviewImage(image.url);
                    setPreviewVisible(true);
                  }}
                />
              </Tooltip>,
              <Tooltip title="复制链接">
                <CopyOutlined onClick={() => handleCopyLink(image.url)} />
              </Tooltip>,
              <Tooltip title="下载">
                <DownloadOutlined onClick={() => handleDownload(image.url, image.name)} />
              </Tooltip>,
              <Popconfirm
                title="确定要删除这张图片吗？"
                onConfirm={() => deleteMutation.mutate(image.id)}
                okText="确定"
                cancelText="取消"
              >
                <DeleteOutlined style={{ color: '#ff4d4f' }} />
              </Popconfirm>
            ]}
          >
            <Card.Meta
              title={
                <Tooltip title={image.name}>
                  <div style={{
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap',
                    fontSize: '12px'
                  }}>
                    {image.name}
                  </div>
                </Tooltip>
              }
              description={
                <div style={{ fontSize: '11px', color: '#999' }}>
                  <div>{formatFileSize(image.size)}</div>
                  <div>{image.width} × {image.height}</div>
                  <div>{formatDate(image.createdAt, 'MM-DD HH:mm')}</div>
                </div>
              }
            />
          </Card>
        </Col>
      ))}
    </Row>
  );
  
  // 列表视图渲染
  const renderListView = () => (
    <List
      dataSource={imageData?.data || []}
      renderItem={(image) => (
        <List.Item
          actions={[
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => {
                setPreviewImage(image.url);
                setPreviewVisible(true);
              }}
            />,
            <Button
              type="text"
              icon={<CopyOutlined />}
              onClick={() => handleCopyLink(image.url)}
            />,
            <Button
              type="text"
              icon={<DownloadOutlined />}
              onClick={() => handleDownload(image.url, image.name)}
            />,
            <Popconfirm
              title="确定要删除这张图片吗？"
              onConfirm={() => deleteMutation.mutate(image.id)}
              okText="确定"
              cancelText="取消"
            >
              <Button type="text" danger icon={<DeleteOutlined />} />
            </Popconfirm>
          ]}
        >
          <List.Item.Meta
            avatar={
              <div style={{ position: 'relative' }}>
                <Avatar
                  shape="square"
                  size={64}
                  src={image.thumbnailUrl || image.url}
                  icon={<FileImageOutlined />}
                  style={{ cursor: 'pointer' }}
                  onClick={() => {
                    setPreviewImage(image.url);
                    setPreviewVisible(true);
                  }}
                />
                <Checkbox
                  style={{
                    position: 'absolute',
                    top: -4,
                    left: -4
                  }}
                  checked={selectedImages.includes(image.id)}
                  onChange={(e) => {
                    if (e.target.checked) {
                      setSelectedImages([...selectedImages, image.id]);
                    } else {
                      setSelectedImages(selectedImages.filter(id => id !== image.id));
                    }
                  }}
                />
              </div>
            }
            title={image.name}
            description={
              <Space direction="vertical" size={4}>
                <Space>
                  <Tag>{image.type}</Tag>
                  <span>{formatFileSize(image.size)}</span>
                  <span>{image.width} × {image.height}</span>
                </Space>
                <Space>
                  <span>上传时间：{formatDate(image.createdAt)}</span>
                  {image.folder && <span>文件夹：{image.folder}</span>}
                </Space>
              </Space>
            }
          />
        </List.Item>
      )}
    />
  );
  
  return (
    <div>
      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="总图片数"
              value={statsData?.total || 0}
              prefix={<FileImageOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="存储空间"
              value={formatFileSize(statsData?.totalSize || 0)}
              prefix={<CloudUploadOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <div>
              <div style={{ fontSize: '14px', color: '#666', marginBottom: '8px' }}>存储使用率</div>
              <Progress
                percent={((statsData?.totalSize || 0) / (10 * 1024 * 1024 * 1024)) * 100}
                size="small"
                format={(percent) => `${percent?.toFixed(1)}%`}
              />
              <div style={{ fontSize: '12px', color: '#999', marginTop: '4px' }}>
                {formatFileSize(statsData?.totalSize || 0)} / 10GB
              </div>
            </div>
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="本月上传"
              value={statsData?.monthlyUploads || 0}
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
      </Row>
      
      <Card>
        {/* 搜索表单 */}
        <Form
          form={searchForm}
          layout="inline"
          onFinish={handleSearch}
          style={{ marginBottom: 16 }}
        >
          <Form.Item name="keyword">
            <Search
              placeholder="搜索图片名称..."
              style={{ width: 200 }}
              onChange={(e) => setFilters(prev => ({ ...prev, keyword: e.target.value }))}
              allowClear
            />
          </Form.Item>
          <Form.Item name="type">
            <Select placeholder="文件类型" style={{ width: 120 }} allowClear>
              <Option value="jpg">JPG</Option>
              <Option value="png">PNG</Option>
              <Option value="gif">GIF</Option>
              <Option value="webp">WebP</Option>
              <Option value="svg">SVG</Option>
            </Select>
          </Form.Item>
          <Form.Item name="folder">
            <Select placeholder="文件夹" style={{ width: 150 }} allowClear>
              {foldersData?.map(folder => (
                <Option key={folder.path} value={folder.path}>
                  <FolderOutlined /> {folder.name}
                </Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item name="dateRange">
            <RangePicker placeholder={['开始日期', '结束日期']} />
          </Form.Item>
          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit" icon={<SearchOutlined />}>
                搜索
              </Button>
              <Button onClick={handleReset}>
                重置
              </Button>
            </Space>
          </Form.Item>
        </Form>
        
        <Divider />
        
        {/* 操作按钮 */}
        <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between' }}>
          <Space>
            <Upload {...uploadProps}>
              <Button type="primary" icon={<PlusOutlined />}>
                上传图片
              </Button>
            </Upload>
            {selectedImages.length > 0 && (
              <Popconfirm
                title={`确定要删除选中的 ${selectedImages.length} 张图片吗？`}
                onConfirm={() => batchDeleteMutation.mutate(selectedImages)}
                okText="确定"
                cancelText="取消"
              >
                <Button
                  danger
                  icon={<DeleteOutlined />}
                  loading={batchDeleteMutation.isLoading}
                >
                  批量删除 ({selectedImages.length})
                </Button>
              </Popconfirm>
            )}
          </Space>
          <Space>
            <Button.Group>
              <Button
                type={viewMode === 'grid' ? 'primary' : 'default'}
                onClick={() => setViewMode('grid')}
              >
                网格
              </Button>
              <Button
                type={viewMode === 'list' ? 'primary' : 'default'}
                onClick={() => setViewMode('list')}
              >
                列表
              </Button>
            </Button.Group>
            <Button icon={<ReloadOutlined />} onClick={() => refetchImages()}>
              刷新
            </Button>
          </Space>
        </div>
        
        {/* 图片展示 */}
        <div style={{ minHeight: 400 }}>
          {imageLoading ? (
            <div style={{ textAlign: 'center', padding: '50px 0' }}>
              <FileImageOutlined style={{ fontSize: '48px', color: '#d9d9d9' }} />
              <div style={{ marginTop: '16px', color: '#999' }}>加载中...</div>
            </div>
          ) : imageData?.data?.length > 0 ? (
            viewMode === 'grid' ? renderGridView() : renderListView()
          ) : (
            <div style={{ textAlign: 'center', padding: '50px 0' }}>
              <FileImageOutlined style={{ fontSize: '48px', color: '#d9d9d9' }} />
              <div style={{ marginTop: '16px', color: '#999' }}>暂无图片</div>
              <Upload {...uploadProps}>
                <Button type="primary" style={{ marginTop: '16px' }}>
                  上传第一张图片
                </Button>
              </Upload>
            </div>
          )}
        </div>
        
        {/* 分页 */}
        {imageData?.data?.length > 0 && (
          <div style={{ textAlign: 'center', marginTop: 24 }}>
            <Button
              disabled={pagination.current === 1}
              onClick={() => setPagination(prev => ({ ...prev, current: prev.current - 1 }))}
            >
              上一页
            </Button>
            <span style={{ margin: '0 16px' }}>
              第 {pagination.current} 页，共 {Math.ceil((imageData?.total || 0) / pagination.pageSize)} 页
            </span>
            <Button
              disabled={pagination.current >= Math.ceil((imageData?.total || 0) / pagination.pageSize)}
              onClick={() => setPagination(prev => ({ ...prev, current: prev.current + 1 }))}
            >
              下一页
            </Button>
          </div>
        )}
      </Card>
      
      {/* 图片预览 */}
      <Image
        width={200}
        style={{ display: 'none' }}
        src={previewImage}
        preview={{
          visible: previewVisible,
          src: previewImage,
          onVisibleChange: (visible) => setPreviewVisible(visible)
        }}
      />
    </div>
  );
};

export default ImageManagement;