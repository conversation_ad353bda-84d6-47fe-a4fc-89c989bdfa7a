import User from '../models/User.js';
import { AppError, catchAsync } from '../middleware/errorHandler.js';
import bcrypt from 'bcryptjs';

// 获取用户列表（管理员功能）
export const getUsers = catchAsync(async (req, res, next) => {
  const {
    page = 1,
    limit = 20,
    sort = '-createdAt',
    status = 'all',
    role = 'all',
    q // 搜索关键词
  } = req.query;
  
  // 构建查询条件
  const query = {};
  
  // 状态过滤
  if (status && status !== 'all') {
    query.status = status;
  }
  
  // 角色过滤
  if (role && role !== 'all') {
    query.role = role;
  }
  
  // 搜索功能
  if (q) {
    query.$or = [
      { username: { $regex: q, $options: 'i' } },
      { email: { $regex: q, $options: 'i' } },
      { displayName: { $regex: q, $options: 'i' } }
    ];
  }
  
  // 计算分页
  const skip = (parseInt(page) - 1) * parseInt(limit);
  
  // 执行查询
  const [users, total] = await Promise.all([
    User.find(query)
      .select('-password -refreshTokens')
      .sort(sort)
      .skip(skip)
      .limit(parseInt(limit))
      .lean(),
    User.countDocuments(query)
  ]);
  
  // 计算分页信息
  const totalPages = Math.ceil(total / parseInt(limit));
  const hasNextPage = parseInt(page) < totalPages;
  const hasPrevPage = parseInt(page) > 1;
  
  res.status(200).json({
    success: true,
    data: {
      users,
      pagination: {
        currentPage: parseInt(page),
        totalPages,
        totalItems: total,
        itemsPerPage: parseInt(limit),
        hasNextPage,
        hasPrevPage
      }
    }
  });
});

// 获取单个用户信息
export const getUser = catchAsync(async (req, res, next) => {
  const { id } = req.params;
  
  const user = await User.findById(id)
    .select('-password -refreshTokens')
    .lean();
  
  if (!user) {
    return next(new AppError('用户不存在', 404));
  }
  
  res.status(200).json({
    success: true,
    data: {
      user
    }
  });
});

// 创建用户（管理员功能）
export const createUser = catchAsync(async (req, res, next) => {
  const { username, email, password, displayName, role = 'user', bio = '' } = req.body;
  
  // 检查用户名和邮箱是否已存在
  const existingUser = await User.findOne({
    $or: [{ username }, { email }]
  });
  
  if (existingUser) {
    if (existingUser.username === username) {
      return next(new AppError('用户名已存在', 400));
    }
    if (existingUser.email === email) {
      return next(new AppError('邮箱已存在', 400));
    }
  }
  
  // 创建新用户
  const newUser = await User.create({
    username,
    email,
    password,
    displayName: displayName || username,
    role,
    bio
  });
  
  // 移除敏感信息
  const userResponse = newUser.toObject();
  delete userResponse.password;
  delete userResponse.refreshTokens;
  
  res.status(201).json({
    success: true,
    message: '用户创建成功',
    data: {
      user: userResponse
    }
  });
});

// 更新用户信息（管理员功能）
export const updateUser = catchAsync(async (req, res, next) => {
  const { id } = req.params;
  const allowedFields = [
    'displayName', 'bio', 'avatar', 'website', 'location', 
    'role', 'status'
  ];
  
  const user = await User.findById(id);
  
  if (!user) {
    return next(new AppError('用户不存在', 404));
  }
  
  // 防止修改超级管理员
  if (user.role === 'admin' && req.user._id.toString() !== user._id.toString()) {
    const adminCount = await User.countDocuments({ role: 'admin', status: 'active' });
    if (adminCount <= 1 && req.body.role !== 'admin') {
      return next(new AppError('不能修改最后一个管理员的角色', 400));
    }
  }
  
  // 过滤允许更新的字段
  const updates = {};
  Object.keys(req.body).forEach(key => {
    if (allowedFields.includes(key)) {
      updates[key] = req.body[key];
    }
  });
  
  // 更新用户信息
  const updatedUser = await User.findByIdAndUpdate(
    id,
    { ...updates, updatedAt: new Date() },
    { new: true, runValidators: true }
  ).select('-password -refreshTokens');
  
  res.status(200).json({
    success: true,
    message: '用户信息更新成功',
    data: {
      user: updatedUser
    }
  });
});

// 重置用户密码（管理员功能）
export const resetUserPassword = catchAsync(async (req, res, next) => {
  const { id } = req.params;
  const { newPassword } = req.body;
  
  if (!newPassword) {
    return next(new AppError('新密码是必需的', 400));
  }
  
  const user = await User.findById(id);
  
  if (!user) {
    return next(new AppError('用户不存在', 404));
  }
  
  // 更新密码
  user.password = newPassword;
  await user.save();
  
  // 清除用户的所有刷新令牌（强制重新登录）
  user.refreshTokens = [];
  await user.save({ validateBeforeSave: false });
  
  res.status(200).json({
    success: true,
    message: '密码重置成功'
  });
});

// 删除用户（管理员功能）
export const deleteUser = catchAsync(async (req, res, next) => {
  const { id } = req.params;
  
  const user = await User.findById(id);
  
  if (!user) {
    return next(new AppError('用户不存在', 404));
  }
  
  // 防止删除自己
  if (req.user._id.toString() === user._id.toString()) {
    return next(new AppError('不能删除自己的账户', 400));
  }
  
  // 防止删除最后一个管理员
  if (user.role === 'admin') {
    const adminCount = await User.countDocuments({ role: 'admin', status: 'active' });
    if (adminCount <= 1) {
      return next(new AppError('不能删除最后一个管理员', 400));
    }
  }
  
  // 软删除用户
  user.status = 'deleted';
  user.deletedAt = new Date();
  user.refreshTokens = [];
  await user.save({ validateBeforeSave: false });
  
  res.status(200).json({
    success: true,
    message: '用户删除成功'
  });
});

// 批量操作用户
export const batchUpdateUsers = catchAsync(async (req, res, next) => {
  const { ids, action } = req.body;
  
  if (!Array.isArray(ids) || ids.length === 0) {
    return next(new AppError('请提供要操作的用户ID列表', 400));
  }
  
  // 检查是否包含当前用户
  if (ids.includes(req.user._id.toString())) {
    return next(new AppError('不能对自己执行批量操作', 400));
  }
  
  let updateData = {};
  let message = '';
  
  switch (action) {
    case 'activate':
      updateData = { status: 'active' };
      message = '用户激活成功';
      break;
    case 'deactivate':
      updateData = { status: 'inactive' };
      message = '用户停用成功';
      break;
    case 'delete':
      // 检查是否包含管理员
      const adminUsers = await User.find({ 
        _id: { $in: ids }, 
        role: 'admin', 
        status: 'active' 
      });
      
      if (adminUsers.length > 0) {
        const remainingAdmins = await User.countDocuments({ 
          role: 'admin', 
          status: 'active',
          _id: { $nin: ids }
        });
        
        if (remainingAdmins === 0) {
          return next(new AppError('操作会删除所有管理员，无法执行', 400));
        }
      }
      
      updateData = { 
        status: 'deleted', 
        deletedAt: new Date(),
        refreshTokens: []
      };
      message = '用户删除成功';
      break;
    default:
      return next(new AppError('无效的操作类型', 400));
  }
  
  const result = await User.updateMany(
    { _id: { $in: ids } },
    updateData
  );
  
  res.status(200).json({
    success: true,
    message,
    data: {
      modifiedCount: result.modifiedCount
    }
  });
});

// 获取用户统计信息
export const getUsersStats = catchAsync(async (req, res, next) => {
  const stats = await User.aggregate([
    {
      $group: {
        _id: null,
        totalUsers: { $sum: 1 },
        activeUsers: {
          $sum: { $cond: [{ $eq: ['$status', 'active'] }, 1, 0] }
        },
        inactiveUsers: {
          $sum: { $cond: [{ $eq: ['$status', 'inactive'] }, 1, 0] }
        },
        deletedUsers: {
          $sum: { $cond: [{ $eq: ['$status', 'deleted'] }, 1, 0] }
        },
        adminUsers: {
          $sum: { $cond: [{ $eq: ['$role', 'admin'] }, 1, 0] }
        },
        editorUsers: {
          $sum: { $cond: [{ $eq: ['$role', 'editor'] }, 1, 0] }
        },
        regularUsers: {
          $sum: { $cond: [{ $eq: ['$role', 'user'] }, 1, 0] }
        }
      }
    }
  ]);
  
  // 获取最近注册的用户
  const recentUsers = await User.find({ status: 'active' })
    .select('username displayName avatar createdAt')
    .sort('-createdAt')
    .limit(10)
    .lean();
  
  // 获取最活跃的用户（基于最后登录时间）
  const activeUsers = await User.find({ 
    status: 'active',
    lastLoginAt: { $exists: true }
  })
    .select('username displayName avatar lastLoginAt')
    .sort('-lastLoginAt')
    .limit(10)
    .lean();
  
  res.status(200).json({
    success: true,
    data: {
      overview: stats[0] || {
        totalUsers: 0,
        activeUsers: 0,
        inactiveUsers: 0,
        deletedUsers: 0,
        adminUsers: 0,
        editorUsers: 0,
        regularUsers: 0
      },
      recentUsers,
      activeUsers
    }
  });
});

// 搜索用户
export const searchUsers = catchAsync(async (req, res, next) => {
  const { q, limit = 10 } = req.query;
  
  if (!q || q.trim().length < 2) {
    return next(new AppError('搜索关键词至少需要2个字符', 400));
  }
  
  const users = await User.find({
    status: 'active',
    $or: [
      { username: { $regex: q, $options: 'i' } },
      { displayName: { $regex: q, $options: 'i' } },
      { email: { $regex: q, $options: 'i' } }
    ]
  })
    .select('username displayName avatar bio')
    .limit(parseInt(limit))
    .lean();
  
  res.status(200).json({
    success: true,
    data: {
      users
    }
  });
});

// 获取用户活动日志（管理员功能）
export const getUserActivity = catchAsync(async (req, res, next) => {
  const { id } = req.params;
  const { page = 1, limit = 20 } = req.query;
  
  const user = await User.findById(id).select('username displayName');
  
  if (!user) {
    return next(new AppError('用户不存在', 404));
  }
  
  // 这里可以扩展为真正的活动日志系统
  // 目前返回基本的用户信息和统计
  const activity = {
    user,
    stats: {
      articlesCount: 0, // 待实现：从Article模型获取
      commentsCount: 0, // 待实现：从Comment模型获取
      imagesCount: 0,   // 待实现：从Image模型获取
      lastLogin: user.lastLoginAt,
      joinDate: user.createdAt
    },
    recentActivity: [] // 待实现：真正的活动日志
  };
  
  res.status(200).json({
    success: true,
    data: {
      activity
    }
  });
});

// 切换用户状态
export const toggleUserStatus = catchAsync(async (req, res, next) => {
  const { id } = req.params;
  
  const user = await User.findById(id);
  
  if (!user) {
    return next(new AppError('用户不存在', 404));
  }
  
  // 防止操作自己
  if (req.user._id.toString() === user._id.toString()) {
    return next(new AppError('不能修改自己的状态', 400));
  }
  
  // 防止停用最后一个管理员
  if (user.role === 'admin' && user.status === 'active') {
    const adminCount = await User.countDocuments({ role: 'admin', status: 'active' });
    if (adminCount <= 1) {
      return next(new AppError('不能停用最后一个管理员', 400));
    }
  }
  
  // 切换状态
  const newStatus = user.status === 'active' ? 'inactive' : 'active';
  user.status = newStatus;
  
  // 如果停用用户，清除其刷新令牌
  if (newStatus === 'inactive') {
    user.refreshTokens = [];
  }
  
  await user.save({ validateBeforeSave: false });
  
  res.status(200).json({
    success: true,
    message: `用户${newStatus === 'active' ? '激活' : '停用'}成功`,
    data: {
      user: {
        _id: user._id,
        username: user.username,
        status: user.status
      }
    }
  });
});

// 导出所有用户控制器
export default {
  getUsers,
  getUser,
  createUser,
  updateUser,
  resetUserPassword,
  deleteUser,
  batchUpdateUsers,
  getUsersStats,
  searchUsers,
  getUserActivity,
  toggleUserStatus
};