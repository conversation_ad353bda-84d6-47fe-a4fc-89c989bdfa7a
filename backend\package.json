{"name": "moying-blog-backend", "version": "1.0.0", "description": "墨影心流博客后端系统", "main": "server.js", "type": "module", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "seed": "node scripts/seed.js"}, "dependencies": {"bcryptjs": "^2.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-mongo-sanitize": "^2.2.0", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.0.3", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "sharp": "^0.33.1", "socket.io": "^4.8.1", "xss-clean": "^0.1.4"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.0.2", "supertest": "^6.3.3"}, "keywords": ["blog", "cms", "express", "mongodb", "rest-api"], "author": "黑猫船长Z", "license": "MIT"}