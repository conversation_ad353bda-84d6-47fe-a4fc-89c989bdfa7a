import bcrypt from 'bcryptjs';

// 创建测试用户数据的异步函数
export const createTestUsers = async () => {
  return [
    {
      _id: '507f1f77bcf86cd799439011',
      username: 'admin',
      email: '<EMAIL>',
      password: await bcrypt.hash('admin123456', 12),
      role: 'admin',
      profile: {
        displayName: '管理员',
        bio: '系统管理员账户'
      },
      status: 'active',
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      _id: '507f1f77bcf86cd799439012',
      username: 'testuser',
      email: '<EMAIL>',
      password: await bcrypt.hash('123456', 12),
      role: 'user',
      profile: {
        displayName: '测试用户',
        bio: '这是一个测试用户账户'
      },
      status: 'active',
      createdAt: new Date(),
      updatedAt: new Date()
    }
  ];
};

// 预定义的测试用户（密码已加密）
export const testUsers = [
  {
    _id: '507f1f77bcf86cd799439011',
    username: 'admin',
    email: '<EMAIL>',
    password: '$2a$12$5EmvsKaDdluZ9XfqcYe9X.WXrb/G.Fiy4YmUqBbDSZ1Sd.kF3xc0K', // admin123456
    role: 'admin',
    permissions: ['read', 'write', 'delete', 'admin'],
    profile: {
      displayName: '管理员',
      bio: '系统管理员账户'
    },
    status: 'active',
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: '507f1f77bcf86cd799439012',
    username: 'testuser',
    email: '<EMAIL>',
    password: '$2a$12$TxwuK48Qk5RgEuLLk6vK.Obf/X6ixy4cqfhvIJTl2RadjSLem903i', // 123456
    role: 'user',
    permissions: ['read', 'write'],
    profile: {
      displayName: '测试用户',
      bio: '这是一个测试用户账户'
    },
    status: 'active',
    createdAt: new Date(),
    updatedAt: new Date()
  }
];

export default testUsers;