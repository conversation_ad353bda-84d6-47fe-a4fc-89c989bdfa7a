import React, { Suspense } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { Layout, Spin } from 'antd';
import { useAuthStore } from '@/store/authStore';
import { useEffect } from 'react';

// 懒加载页面组件
const LoginPage = React.lazy(() => import('@/pages/LoginPage'));
const DashboardLayout = React.lazy(() => import('@/components/layout/DashboardLayout'));
const Dashboard = React.lazy(() => import('@/pages/Dashboard'));
const ArticleList = React.lazy(() => import('@/pages/ArticleList'));
const ArticleEdit = React.lazy(() => import('@/pages/ArticleEdit'));
const ImageManagement = React.lazy(() => import('@/pages/ImageManagement'));
const UserManagement = React.lazy(() => import('@/pages/UserManagement'));
const Settings = React.lazy(() => import('@/pages/Settings'));
const Profile = React.lazy(() => import('@/pages/Profile'));

// 加载组件
const PageLoading = () => (
  <div style={{
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    minHeight: '200px'
  }}>
    <Spin size="large" tip="页面加载中..." />
  </div>
);

// 受保护的路由组件
const ProtectedRoute = ({ children }) => {
  const { isAuthenticated, isLoading } = useAuthStore();
  
  if (isLoading) {
    return <PageLoading />;
  }
  
  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }
  
  return children;
};

// 公开路由组件（已登录用户重定向到仪表板）
const PublicRoute = ({ children }) => {
  const { isAuthenticated, isLoading } = useAuthStore();
  
  if (isLoading) {
    return <PageLoading />;
  }
  
  if (isAuthenticated) {
    return <Navigate to="/dashboard" replace />;
  }
  
  return children;
};

function App() {
  const { initializeAuth } = useAuthStore();
  
  // 应用初始化时检查认证状态
  useEffect(() => {
    initializeAuth();
  }, [initializeAuth]);
  
  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Suspense fallback={<PageLoading />}>
        <Routes>
          {/* 公开路由 */}
          <Route 
            path="/login" 
            element={
              <PublicRoute>
                <LoginPage />
              </PublicRoute>
            } 
          />
          
          {/* 受保护的路由 */}
          <Route 
            path="/*" 
            element={
              <ProtectedRoute>
                <DashboardLayout>
                  <Routes>
                    {/* 仪表板 */}
                    <Route path="/dashboard" element={<Dashboard />} />
                    
                    {/* 文章管理 */}
                    <Route path="/articles" element={<ArticleList />} />
                    <Route path="/articles/new" element={<ArticleEdit />} />
                    <Route path="/articles/edit/:id" element={<ArticleEdit />} />
                    
                    {/* 图片管理 */}
                    <Route path="/images" element={<ImageManagement />} />
                    
                    {/* 用户管理 */}
                    <Route path="/users" element={<UserManagement />} />
                    
                    {/* 个人资料 */}
                    <Route path="/profile" element={<Profile />} />
                    
                    {/* 系统设置 */}
                    <Route path="/settings" element={<Settings />} />
                    
                    {/* 默认重定向到仪表板 */}
                    <Route path="/" element={<Navigate to="/dashboard" replace />} />
                    
                    {/* 404页面 */}
                    <Route 
                      path="*" 
                      element={
                        <div style={{
                          display: 'flex',
                          flexDirection: 'column',
                          alignItems: 'center',
                          justifyContent: 'center',
                          minHeight: '400px',
                          textAlign: 'center'
                        }}>
                          <h1 style={{ fontSize: '72px', margin: 0, color: '#ccc' }}>404</h1>
                          <h2 style={{ color: '#666', marginBottom: '16px' }}>页面未找到</h2>
                          <p style={{ color: '#999' }}>抱歉，您访问的页面不存在。</p>
                        </div>
                      } 
                    />
                  </Routes>
                </DashboardLayout>
              </ProtectedRoute>
            } 
          />
        </Routes>
      </Suspense>
    </Layout>
  );
}

export default App;