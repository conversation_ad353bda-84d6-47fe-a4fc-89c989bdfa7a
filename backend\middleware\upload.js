import multer from 'multer';
import path from 'path';
import fs from 'fs/promises';
import sharp from 'sharp';
import { AppError } from './errorHandler.js';

// 确保上传目录存在
const ensureDirectoryExists = async (dirPath) => {
  try {
    await fs.access(dirPath);
  } catch (error) {
    if (error.code === 'ENOENT') {
      await fs.mkdir(dirPath, { recursive: true });
    } else {
      throw error;
    }
  }
};

// 生成唯一文件名
const generateUniqueFilename = (originalname) => {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substring(2, 15);
  const ext = path.extname(originalname).toLowerCase();
  return `${timestamp}-${random}${ext}`;
};

// 配置存储
const storage = multer.diskStorage({
  destination: async (req, file, cb) => {
    try {
      const uploadDir = path.join(process.cwd(), 'uploads');
      await ensureDirectoryExists(uploadDir);
      
      // 根据用途创建子目录
      const purpose = req.body.purpose || 'general';
      const subDir = path.join(uploadDir, purpose);
      await ensureDirectoryExists(subDir);
      
      cb(null, subDir);
    } catch (error) {
      cb(error);
    }
  },
  filename: (req, file, cb) => {
    const uniqueName = generateUniqueFilename(file.originalname);
    cb(null, uniqueName);
  }
});

// 文件过滤器
const fileFilter = (req, file, cb) => {
  // 检查文件类型
  const allowedTypes = {
    'image/jpeg': '.jpg',
    'image/jpg': '.jpg',
    'image/png': '.png',
    'image/webp': '.webp',
    'image/gif': '.gif'
  };
  
  if (allowedTypes[file.mimetype]) {
    cb(null, true);
  } else {
    cb(new AppError('只支持图片文件 (JPEG, PNG, WebP, GIF)', 400), false);
  }
};

// 基础上传配置
const uploadConfig = {
  storage,
  fileFilter,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB
    files: 5 // 最多5个文件
  }
};

// 创建不同用途的上传中间件
export const uploadSingle = multer(uploadConfig).single('image');
export const uploadMultiple = multer(uploadConfig).array('images', 5);
export const uploadFields = multer(uploadConfig).fields([
  { name: 'coverImage', maxCount: 1 },
  { name: 'images', maxCount: 10 }
]);

// 图片处理中间件
export const processImage = async (req, res, next) => {
  try {
    if (!req.file && !req.files) {
      return next();
    }
    
    const processFile = async (file) => {
      const { path: filePath, filename } = file;
      const outputDir = path.dirname(filePath);
      
      // 获取图片信息
      const image = sharp(filePath);
      const metadata = await image.metadata();
      
      // 生成不同尺寸的图片
      const sizes = {
        thumbnail: { width: 150, height: 150 },
        small: { width: 400, height: 300 },
        medium: { width: 800, height: 600 },
        large: { width: 1200, height: 900 }
      };
      
      const processedImages = {};
      const baseName = path.parse(filename).name;
      const ext = path.parse(filename).ext;
      
      // 处理原图（优化质量）
      const optimizedPath = path.join(outputDir, `${baseName}-optimized${ext}`);
      await image
        .jpeg({ quality: 85, progressive: true })
        .png({ compressionLevel: 8 })
        .webp({ quality: 85 })
        .toFile(optimizedPath);
      
      processedImages.original = {
        path: optimizedPath,
        width: metadata.width,
        height: metadata.height,
        size: (await fs.stat(optimizedPath)).size
      };
      
      // 生成不同尺寸
      for (const [sizeName, dimensions] of Object.entries(sizes)) {
        const resizedPath = path.join(outputDir, `${baseName}-${sizeName}${ext}`);
        
        await image
          .resize(dimensions.width, dimensions.height, {
            fit: 'inside',
            withoutEnlargement: true
          })
          .jpeg({ quality: 80 })
          .png({ compressionLevel: 8 })
          .webp({ quality: 80 })
          .toFile(resizedPath);
        
        const resizedStats = await fs.stat(resizedPath);
        const resizedMetadata = await sharp(resizedPath).metadata();
        
        processedImages[sizeName] = {
          path: resizedPath,
          width: resizedMetadata.width,
          height: resizedMetadata.height,
          size: resizedStats.size
        };
      }
      
      // 删除原始上传文件
      await fs.unlink(filePath);
      
      // 更新文件信息
      file.processedImages = processedImages;
      file.metadata = {
        width: metadata.width,
        height: metadata.height,
        format: metadata.format,
        channels: metadata.channels,
        density: metadata.density
      };
      
      return file;
    };
    
    // 处理单个文件
    if (req.file) {
      req.file = await processFile(req.file);
    }
    
    // 处理多个文件
    if (req.files) {
      if (Array.isArray(req.files)) {
        req.files = await Promise.all(req.files.map(processFile));
      } else {
        // 处理字段形式的文件
        for (const [fieldName, files] of Object.entries(req.files)) {
          req.files[fieldName] = await Promise.all(files.map(processFile));
        }
      }
    }
    
    next();
  } catch (error) {
    console.error('图片处理错误:', error);
    next(new AppError('图片处理失败', 500));
  }
};

// 清理临时文件中间件
export const cleanupTempFiles = (req, res, next) => {
  const originalSend = res.send;
  
  res.send = function(data) {
    // 在响应发送后清理临时文件
    setImmediate(async () => {
      try {
        const filesToClean = [];
        
        if (req.file && req.file.path) {
          filesToClean.push(req.file.path);
        }
        
        if (req.files) {
          if (Array.isArray(req.files)) {
            req.files.forEach(file => {
              if (file.path) filesToClean.push(file.path);
            });
          } else {
            Object.values(req.files).flat().forEach(file => {
              if (file.path) filesToClean.push(file.path);
            });
          }
        }
        
        // 只在出错时清理文件
        if (res.statusCode >= 400) {
          await Promise.all(
            filesToClean.map(async (filePath) => {
              try {
                await fs.unlink(filePath);
              } catch (error) {
                console.error('清理临时文件失败:', error);
              }
            })
          );
        }
      } catch (error) {
        console.error('清理过程出错:', error);
      }
    });
    
    originalSend.call(this, data);
  };
  
  next();
};

// 验证图片尺寸中间件
export const validateImageDimensions = (options = {}) => {
  const {
    minWidth = 0,
    minHeight = 0,
    maxWidth = 5000,
    maxHeight = 5000,
    aspectRatio = null // 例如: 16/9
  } = options;
  
  return async (req, res, next) => {
    try {
      if (!req.file && !req.files) {
        return next();
      }
      
      const validateFile = async (file) => {
        const image = sharp(file.path);
        const metadata = await image.metadata();
        
        if (metadata.width < minWidth || metadata.height < minHeight) {
          throw new AppError(`图片尺寸太小，最小尺寸为 ${minWidth}x${minHeight}`, 400);
        }
        
        if (metadata.width > maxWidth || metadata.height > maxHeight) {
          throw new AppError(`图片尺寸太大，最大尺寸为 ${maxWidth}x${maxHeight}`, 400);
        }
        
        if (aspectRatio) {
          const fileAspectRatio = metadata.width / metadata.height;
          const tolerance = 0.1; // 10%的容差
          
          if (Math.abs(fileAspectRatio - aspectRatio) > tolerance) {
            throw new AppError(`图片宽高比不符合要求，期望比例为 ${aspectRatio}`, 400);
          }
        }
      };
      
      if (req.file) {
        await validateFile(req.file);
      }
      
      if (req.files) {
        const files = Array.isArray(req.files) ? req.files : Object.values(req.files).flat();
        await Promise.all(files.map(validateFile));
      }
      
      next();
    } catch (error) {
      next(error);
    }
  };
};

// 获取文件URL的辅助函数
export const getFileUrl = (req, filePath) => {
  const baseUrl = `${req.protocol}://${req.get('host')}`;
  const relativePath = path.relative(process.cwd(), filePath).replace(/\\/g, '/');
  return `${baseUrl}/${relativePath}`;
};

// 删除文件的辅助函数
export const deleteFile = async (filePath) => {
  try {
    await fs.unlink(filePath);
    return true;
  } catch (error) {
    console.error('删除文件失败:', error);
    return false;
  }
};

// 删除处理过的图片文件
export const deleteProcessedImages = async (processedImages) => {
  if (!processedImages) return;
  
  const deletePromises = Object.values(processedImages).map(async (imageInfo) => {
    if (imageInfo.path) {
      await deleteFile(imageInfo.path);
    }
  });
  
  await Promise.all(deletePromises);
};

// 导出所有上传相关功能
export default {
  uploadSingle,
  uploadMultiple,
  uploadFields,
  processImage,
  cleanupTempFiles,
  validateImageDimensions,
  getFileUrl,
  deleteFile,
  deleteProcessedImages
};