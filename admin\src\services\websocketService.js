import { io } from 'socket.io-client';
import { message } from 'antd';

class WebSocketService {
  constructor() {
    this.socket = null;
    this.isConnected = false;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectDelay = 1000;
    this.eventListeners = new Map();
  }

  // 连接WebSocket服务器
  connect() {
    const serverUrl = import.meta.env.VITE_API_BASE_URL?.replace('/api', '') || 'http://localhost:5000';
    const token = localStorage.getItem('token');

    console.log('🔗 连接WebSocket服务器:', serverUrl);

    this.socket = io(serverUrl, {
      auth: {
        token: token
      },
      transports: ['websocket', 'polling'],
      timeout: 10000,
      forceNew: true
    });

    this.setupEventListeners();
  }

  // 设置事件监听器
  setupEventListeners() {
    if (!this.socket) return;

    // 连接成功
    this.socket.on('connect', () => {
      console.log('✅ WebSocket连接成功');
      this.isConnected = true;
      this.reconnectAttempts = 0;
      
      // 加入管理员房间
      this.socket.emit('join-room', 'admin');
      
      message.success('实时同步已启用');
    });

    // 连接确认
    this.socket.on('connected', (data) => {
      console.log('📡 连接确认:', data);
    });

    // 数据变更事件
    this.socket.on('data-change', (event) => {
      console.log('📊 收到数据变更事件:', event);
      this.handleDataChange(event);
    });

    // 房间加入确认
    this.socket.on('room-joined', (data) => {
      console.log('🏠 加入房间:', data.room);
    });

    // 连接断开
    this.socket.on('disconnect', (reason) => {
      console.log('🔌 WebSocket连接断开:', reason);
      this.isConnected = false;
      
      if (reason === 'io server disconnect') {
        // 服务器主动断开，需要重新连接
        this.reconnect();
      }
    });

    // 连接错误
    this.socket.on('connect_error', (error) => {
      console.error('❌ WebSocket连接错误:', error);
      this.isConnected = false;
      this.reconnect();
    });

    // 心跳响应
    this.socket.on('pong', (data) => {
      console.log('💓 心跳响应:', data);
    });
  }

  // 处理数据变更事件
  handleDataChange(event) {
    const { type, data, timestamp } = event;
    
    // 触发注册的监听器
    const listeners = this.eventListeners.get(type) || [];
    listeners.forEach(callback => {
      try {
        callback(data, event);
      } catch (error) {
        console.error('事件处理器执行错误:', error);
      }
    });

    // 显示通知
    this.showChangeNotification(type, data);
  }

  // 显示变更通知
  showChangeNotification(type, data) {
    const notifications = {
      'article:created': () => message.info(`新文章已创建: ${data.title}`),
      'article:updated': () => message.info(`文章已更新: ${data.title}`),
      'article:deleted': () => message.warning(`文章已删除: ${data.title}`),
      'article:published': () => message.success(`文章已发布: ${data.title}`),
      'article:unpublished': () => message.warning(`文章已取消发布: ${data.title}`),
      'image:uploaded': () => message.success(`图片已上传: ${data.filename || '新图片'}`),
      'image:updated': () => message.info(`图片信息已更新`),
      'image:deleted': () => message.warning(`图片已删除`),
      'system:cache_clear': () => message.warning(data.message || '系统缓存已清除')
    };

    const notificationFn = notifications[type];
    if (notificationFn) {
      notificationFn();
    }
  }

  // 注册事件监听器
  on(eventType, callback) {
    if (!this.eventListeners.has(eventType)) {
      this.eventListeners.set(eventType, []);
    }
    this.eventListeners.get(eventType).push(callback);

    // 返回取消监听的函数
    return () => {
      const listeners = this.eventListeners.get(eventType);
      if (listeners) {
        const index = listeners.indexOf(callback);
        if (index > -1) {
          listeners.splice(index, 1);
        }
      }
    };
  }

  // 移除事件监听器
  off(eventType, callback) {
    const listeners = this.eventListeners.get(eventType);
    if (listeners) {
      const index = listeners.indexOf(callback);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  // 重新连接
  reconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('❌ WebSocket重连次数已达上限');
      message.error('实时同步连接失败，请刷新页面重试');
      return;
    }

    this.reconnectAttempts++;
    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);
    
    console.log(`🔄 ${delay}ms后尝试第${this.reconnectAttempts}次重连...`);
    
    setTimeout(() => {
      if (!this.isConnected) {
        this.connect();
      }
    }, delay);
  }

  // 发送心跳
  ping() {
    if (this.socket && this.isConnected) {
      this.socket.emit('ping');
    }
  }

  // 断开连接
  disconnect() {
    if (this.socket) {
      console.log('🔌 主动断开WebSocket连接');
      this.socket.disconnect();
      this.socket = null;
      this.isConnected = false;
    }
  }

  // 获取连接状态
  getConnectionStatus() {
    return {
      isConnected: this.isConnected,
      reconnectAttempts: this.reconnectAttempts,
      socketId: this.socket?.id
    };
  }

  // 清除所有缓存
  clearAllCaches() {
    if (this.socket && this.isConnected) {
      this.socket.emit('clear-caches');
    }
  }
}

// 创建单例实例
const websocketService = new WebSocketService();

export default websocketService;