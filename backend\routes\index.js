import express from 'express';
import authRoutes from './auth.js';
import tempAuthRoutes from './tempAuth.js';
import articleRoutes from './articles.js';
import imageRoutes from './images.js';
import userRoutes from './users.js';

const router = express.Router();

// API版本信息
router.get('/', (req, res) => {
  res.json({
    success: true,
    message: '墨影博客 API v1.0',
    version: '1.0.0',
    timestamp: new Date().toISOString(),
    endpoints: {
      auth: '/api/auth',
      articles: '/api/articles',
      images: '/api/images',
      users: '/api/users'
    },
    documentation: {
      swagger: '/api/docs',
      postman: '/api/postman'
    }
  });
});

// 健康检查端点
router.get('/health', async (req, res) => {
  try {
    const mongoose = await import('mongoose');
    const dbStatus = mongoose.default.connection.readyState;
    const dbStatusMap = {
      0: 'disconnected',
      1: 'connected',
      2: 'connecting',
      3: 'disconnecting'
    };

    const memoryUsage = process.memoryUsage();
    const healthData = {
      success: true,
      status: dbStatus === 1 ? 'healthy' : 'unhealthy',
      timestamp: new Date().toISOString(),
      uptime: Math.floor(process.uptime()),
      version: process.env.npm_package_version || '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      database: {
        status: dbStatusMap[dbStatus] || 'unknown',
        connected: dbStatus === 1
      },
      memory: {
        used: Math.round(memoryUsage.heapUsed / 1024 / 1024 * 100) / 100,
        total: Math.round(memoryUsage.heapTotal / 1024 / 1024 * 100) / 100,
        external: Math.round(memoryUsage.external / 1024 / 1024 * 100) / 100,
        rss: Math.round(memoryUsage.rss / 1024 / 1024 * 100) / 100
      },
      cpu: {
        usage: process.cpuUsage()
      }
    };

    const statusCode = healthData.status === 'healthy' ? 200 : 503;
    res.status(statusCode).json(healthData);
  } catch (error) {
    res.status(503).json({
      success: false,
      status: 'error',
      message: '健康检查失败',
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// 挂载各模块路由
router.use('/auth', authRoutes);
router.use('/temp-auth', tempAuthRoutes); // 临时认证路由
router.use('/articles', articleRoutes);
router.use('/images', imageRoutes);
router.use('/users', userRoutes);

// 404处理
router.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: `API端点 ${req.originalUrl} 不存在`,
    availableEndpoints: {
      auth: '/api/auth',
      articles: '/api/articles',
      images: '/api/images',
      users: '/api/users'
    }
  });
});

export default router;