import axios from 'axios';
import { message } from 'antd';

// 创建 axios 实例
const api = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:5000/api',
  timeout: 30000, // 增加超时时间
  headers: {
    'Content-Type': 'application/json'
  },
  withCredentials: true // 支持跨域cookie
});

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    // 添加认证 token
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    
    // 添加请求时间戳（防止缓存）
    if (config.method === 'get') {
      config.params = {
        ...config.params,
        _t: Date.now()
      };
    }
    
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    return response.data;
  },
  async (error) => {
    const { response, config } = error;
    
    if (!response) {
      // 网络错误或超时
      if (error.code === 'ECONNABORTED') {
        message.error('请求超时，请稍后重试');
      } else {
        message.error('网络连接失败，请检查网络设置');
      }
      return Promise.reject(error);
    }
    
    const { status, data } = response;
    
    // 处理不同的HTTP状态码
    switch (status) {
      case 401:
        // 未授权，尝试刷新token
        if (!config._retry && localStorage.getItem('refreshToken')) {
          config._retry = true;
          try {
            const refreshToken = localStorage.getItem('refreshToken');
            const refreshResponse = await api.post('/auth/refresh', { refreshToken });
            const { token } = refreshResponse.data;
            localStorage.setItem('token', token);
            config.headers.Authorization = `Bearer ${token}`;
            return api(config);
          } catch (refreshError) {
            // 刷新失败，清除token并跳转到登录页
            localStorage.removeItem('token');
            localStorage.removeItem('refreshToken');
            window.location.href = '/login';
            message.error('登录已过期，请重新登录');
          }
        } else {
          localStorage.removeItem('token');
          localStorage.removeItem('refreshToken');
          window.location.href = '/login';
          message.error('登录已过期，请重新登录');
        }
        break;
        
      case 403:
        message.error(data.message || '权限不足，无法执行此操作');
        break;
        
      case 404:
        message.error(data.message || '请求的资源不存在');
        break;
        
      case 400:
      case 422:
        // 处理验证错误
        if (data.errors) {
          const errorMessages = Object.values(data.errors).flat();
          message.error(errorMessages[0] || '数据验证失败');
        } else {
          message.error(data.message || '数据验证失败');
        }
        break;
        
      case 429:
        const retryAfter = response.headers['retry-after'];
        const waitTime = retryAfter ? `${retryAfter}秒` : '稍后';
        message.error(`请求过于频繁，请${waitTime}再试`);
        break;
        
      case 500:
        message.error(data.message || '服务器内部错误，请稍后重试');
        break;
        
      case 502:
      case 503:
      case 504:
        message.error('服务暂时不可用，请稍后重试');
        break;
        
      default:
        message.error(data.message || `请求失败 (${status})`);
    }
    
    return Promise.reject(error);
  }
);

// 认证相关 API
export const authAPI = {
  // 登录 - 使用临时认证端点
  login: (credentials) => api.post('/temp-auth/login', credentials),
  
  // 登出
  logout: () => api.post('/auth/logout'),
  
  // 获取用户信息
  getProfile: () => api.get('/auth/profile'),
  
  // 更新用户信息
  updateProfile: (data) => api.put('/auth/profile', data),
  
  // 更新密码
  updatePassword: (data) => api.put('/auth/password', data),
  
  // 刷新 token
  refreshToken: () => api.post('/auth/refresh')
};

// 文章相关 API
export const articleAPI = {
  // 获取文章列表
  getList: (params) => api.get('/articles', { params }),
  
  // 获取文章详情
  getById: (id) => api.get(`/articles/${id}`),
  
  // 创建文章
  create: (data) => api.post('/articles', data),
  
  // 更新文章
  update: (id, data) => api.put(`/articles/${id}`, data),
  
  // 删除文章
  delete: (id) => api.delete(`/articles/${id}`),
  
  // 批量删除文章
  batchDelete: (ids) => api.delete('/articles/batch', { data: { ids } }),
  
  // 发布文章
  publish: (id) => api.put(`/articles/${id}/publish`),
  
  // 取消发布
  unpublish: (id) => api.put(`/articles/${id}/unpublish`),
  
  // 获取文章统计
  getStats: () => api.get('/articles/stats'),
  
  // 搜索文章
  search: (params) => api.get('/articles/search', { params })
};

// 图片相关 API
export const imageAPI = {
  // 获取图片列表
  getList: (params) => api.get('/images', { params }),
  
  // 上传图片
  upload: (formData, onProgress) => {
    return api.post('/images/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      onUploadProgress: onProgress
    });
  },
  
  // 批量上传图片
  batchUpload: (formData, onProgress) => {
    return api.post('/images/batch-upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      onUploadProgress: onProgress
    });
  },
  
  // 获取图片详情
  getById: (id) => api.get(`/images/${id}`),
  
  // 更新图片信息
  update: (id, data) => api.put(`/images/${id}`, data),
  
  // 删除图片
  delete: (id) => api.delete(`/images/${id}`),
  
  // 批量删除图片
  batchDelete: (ids) => api.delete('/images/batch', { data: { ids } }),
  
  // 添加图片引用
  addReference: (id, data) => api.post(`/images/${id}/references`, data),
  
  // 移除图片引用
  removeReference: (id, referenceId) => api.delete(`/images/${id}/references/${referenceId}`),
  
  // 获取图片统计
  getStats: () => api.get('/images/stats'),
  
  // 清理未使用的图片
  cleanup: () => api.post('/images/cleanup')
};

// 用户相关 API
export const userAPI = {
  // 获取用户列表
  getList: (params) => api.get('/users', { params }),
  
  // 获取用户详情
  getById: (id) => api.get(`/users/${id}`),
  
  // 创建用户
  create: (data) => api.post('/users', data),
  
  // 更新用户
  update: (id, data) => api.put(`/users/${id}`, data),
  
  // 删除用户
  delete: (id) => api.delete(`/users/${id}`),
  
  // 批量删除用户
  batchDelete: (ids) => api.delete('/users/batch', { data: { ids } }),
  
  // 重置用户密码
  resetPassword: (id, data) => api.put(`/users/${id}/password`, data),
  
  // 切换用户状态
  toggleStatus: (id) => api.put(`/users/${id}/toggle-status`),
  
  // 获取用户统计
  getStats: () => api.get('/users/stats'),
  
  // 搜索用户
  search: (params) => api.get('/users/search', { params }),
  
  // 获取用户活动日志
  getActivityLogs: (id, params) => api.get(`/users/${id}/activity-logs`, { params })
};

// 系统相关 API
export const systemAPI = {
  // 获取系统信息
  getInfo: () => api.get('/system/info'),
  
  // 获取系统统计
  getStats: () => api.get('/system/stats'),
  
  // 获取系统日志
  getLogs: (params) => api.get('/system/logs', { params }),
  
  // 清理系统日志
  clearLogs: () => api.delete('/system/logs'),
  
  // 获取系统设置
  getSettings: () => api.get('/system/settings'),
  
  // 更新系统设置
  updateSettings: (data) => api.put('/system/settings', data),
  
  // 备份数据
  backup: () => api.post('/system/backup'),
  
  // 获取备份列表
  getBackups: () => api.get('/system/backups'),
  
  // 恢复数据
  restore: (backupId) => api.post(`/system/restore/${backupId}`),
  
  // 健康检查
  healthCheck: () => api.get('/system/health')
};

// 文件上传辅助函数
export const uploadFile = async (file, onProgress) => {
  const formData = new FormData();
  formData.append('file', file);
  
  return imageAPI.upload(formData, (progressEvent) => {
    if (onProgress) {
      const percentCompleted = Math.round(
        (progressEvent.loaded * 100) / progressEvent.total
      );
      onProgress(percentCompleted);
    }
  });
};

// 批量上传文件辅助函数
export const uploadFiles = async (files, onProgress) => {
  const formData = new FormData();
  files.forEach((file) => {
    formData.append('files', file);
  });
  
  return imageAPI.batchUpload(formData, (progressEvent) => {
    if (onProgress) {
      const percentCompleted = Math.round(
        (progressEvent.loaded * 100) / progressEvent.total
      );
      onProgress(percentCompleted);
    }
  });
};

// 导出默认实例
export default api;