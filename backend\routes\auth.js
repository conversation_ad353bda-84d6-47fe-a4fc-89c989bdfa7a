import express from 'express';
import {
  register,
  login,
  refreshToken,
  logout,
  getMe as getProfile,
  updateMe as updateProfile,
  changePassword,
  deleteAccount,
  checkUsername as checkUsernameAvailability,
  checkEmail as checkEmailAvailability,
  getUserStats,
  verifyToken
} from '../controllers/authController.js';
import {
  authenticate as authenticateToken,
  optionalAuth,
  verifyRefreshToken as requireRefreshToken
} from '../middleware/auth.js';
import {
  validateUserRegistration as validateRegister,
  validateUserLogin as validateLogin,
  validatePasswordChange,
  validateUserUpdate as validateProfileUpdate,
  validateRefreshToken,
  validateObjectId as validateUsernameCheck,
  validateObjectId as validateEmailCheck
} from '../middleware/validation.js';

const router = express.Router();

// 公开路由
// 用户注册
router.post('/register', validateRegister, register);

// 用户登录
router.post('/login', validateLogin, login);

// 刷新访问令牌
router.post('/refresh', validateRefreshToken, requireRefreshToken, refreshToken);

// 检查用户名可用性
router.get('/check-username/:username', validateUsernameCheck, checkUsernameAvailability);

// 检查邮箱可用性
router.get('/check-email/:email', validateEmailCheck, checkEmailAvailability);

// 验证令牌（可选认证）
router.get('/verify', optionalAuth, verifyToken);

// 需要认证的路由
// 用户登出
router.post('/logout', authenticateToken, logout);

// 获取用户资料
router.get('/profile', authenticateToken, getProfile);

// 更新用户资料
router.put('/profile', authenticateToken, validateProfileUpdate, updateProfile);

// 修改密码
router.put('/password', authenticateToken, validatePasswordChange, changePassword);

// 获取用户统计信息
router.get('/stats', authenticateToken, getUserStats);

// 删除账户
router.delete('/account', authenticateToken, deleteAccount);

export default router;