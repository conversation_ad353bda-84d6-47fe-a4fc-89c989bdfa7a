import React, { useState } from 'react';
import {
  Table,
  Card,
  Button,
  Space,
  Input,
  Select,
  Tag,
  Modal,
  Form,
  message,
  Popconfirm,
  Avatar,
  Tooltip,
  DatePicker,
  Row,
  Col,
  Statistic,
  Divider,
  Switch,
  Badge
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  SearchOutlined,
  ReloadOutlined,
  UserOutlined,
  MailOutlined,
  PhoneOutlined,
  LockOutlined,
  UnlockOutlined,
  CrownOutlined,
  TeamOutlined
} from '@ant-design/icons';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { userAPI } from '@/services/api';
import { formatDate } from '@/utils';
import { useDebounce } from '@/hooks';

const { Search } = Input;
const { Option } = Select;
const { RangePicker } = DatePicker;

const UserManagement = () => {
  const queryClient = useQueryClient();
  const [searchForm] = Form.useForm();
  const [userForm] = Form.useForm();
  
  // 状态管理
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  });
  
  const [filters, setFilters] = useState({
    keyword: '',
    role: '',
    status: '',
    dateRange: null
  });
  
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [modalVisible, setModalVisible] = useState(false);
  const [modalType, setModalType] = useState('create'); // create | edit
  const [currentUser, setCurrentUser] = useState(null);
  
  // 防抖搜索
  const debouncedKeyword = useDebounce(filters.keyword, 500);
  
  // 获取用户列表
  const {
    data: userData,
    loading: userLoading,
    refetch: refetchUsers
  } = useQuery({
    queryKey: ['users', pagination.current, pagination.pageSize, debouncedKeyword, filters],
    queryFn: () => userAPI.getList({
      page: pagination.current,
      pageSize: pagination.pageSize,
      keyword: debouncedKeyword,
      role: filters.role,
      status: filters.status,
      startDate: filters.dateRange?.[0]?.format('YYYY-MM-DD'),
      endDate: filters.dateRange?.[1]?.format('YYYY-MM-DD')
    }),
    keepPreviousData: true
  });
  
  // 获取统计数据
  const { data: statsData } = useQuery({
    queryKey: ['user-stats'],
    queryFn: () => userAPI.getStats()
  });
  
  // 创建用户
  const createMutation = useMutation({
    mutationFn: userAPI.create,
    onSuccess: () => {
      message.success('用户创建成功');
      queryClient.invalidateQueries(['users']);
      queryClient.invalidateQueries(['user-stats']);
      setModalVisible(false);
      userForm.resetFields();
    },
    onError: (error) => {
      message.error(error.message || '创建失败');
    }
  });
  
  // 更新用户
  const updateMutation = useMutation({
    mutationFn: ({ id, data }) => userAPI.update(id, data),
    onSuccess: () => {
      message.success('用户更新成功');
      queryClient.invalidateQueries(['users']);
      setModalVisible(false);
      userForm.resetFields();
    },
    onError: (error) => {
      message.error(error.message || '更新失败');
    }
  });
  
  // 删除用户
  const deleteMutation = useMutation({
    mutationFn: userAPI.delete,
    onSuccess: () => {
      message.success('删除成功');
      queryClient.invalidateQueries(['users']);
      queryClient.invalidateQueries(['user-stats']);
      setSelectedRowKeys([]);
    },
    onError: (error) => {
      message.error(error.message || '删除失败');
    }
  });
  
  // 批量删除
  const batchDeleteMutation = useMutation({
    mutationFn: (ids) => userAPI.batchDelete(ids),
    onSuccess: () => {
      message.success('批量删除成功');
      queryClient.invalidateQueries(['users']);
      queryClient.invalidateQueries(['user-stats']);
      setSelectedRowKeys([]);
    },
    onError: (error) => {
      message.error(error.message || '批量删除失败');
    }
  });
  
  // 更新用户状态
  const updateStatusMutation = useMutation({
    mutationFn: ({ id, status }) => userAPI.updateStatus(id, status),
    onSuccess: () => {
      message.success('状态更新成功');
      queryClient.invalidateQueries(['users']);
    },
    onError: (error) => {
      message.error(error.message || '状态更新失败');
    }
  });
  
  // 重置密码
  const resetPasswordMutation = useMutation({
    mutationFn: userAPI.resetPassword,
    onSuccess: () => {
      message.success('密码重置成功，新密码已发送到用户邮箱');
    },
    onError: (error) => {
      message.error(error.message || '密码重置失败');
    }
  });
  
  // 处理搜索
  const handleSearch = (values) => {
    setFilters(prev => ({ ...prev, ...values }));
    setPagination(prev => ({ ...prev, current: 1 }));
  };
  
  // 重置搜索
  const handleReset = () => {
    searchForm.resetFields();
    setFilters({
      keyword: '',
      role: '',
      status: '',
      dateRange: null
    });
    setPagination(prev => ({ ...prev, current: 1 }));
  };
  
  // 处理表格变化
  const handleTableChange = (paginationConfig) => {
    setPagination({
      ...pagination,
      current: paginationConfig.current,
      pageSize: paginationConfig.pageSize
    });
  };
  
  // 打开用户模态框
  const openUserModal = (type, user = null) => {
    setModalType(type);
    setCurrentUser(user);
    setModalVisible(true);
    
    if (type === 'edit' && user) {
      userForm.setFieldsValue({
        username: user.username,
        email: user.email,
        phone: user.phone,
        role: user.role,
        status: user.status,
        nickname: user.nickname,
        bio: user.bio
      });
    } else {
      userForm.resetFields();
    }
  };
  
  // 提交用户表单
  const handleUserSubmit = async (values) => {
    if (modalType === 'create') {
      createMutation.mutate(values);
    } else {
      updateMutation.mutate({ id: currentUser.id, data: values });
    }
  };
  
  // 表格列定义
  const columns = [
    {
      title: '用户信息',
      key: 'userInfo',
      width: 250,
      render: (_, record) => (
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <Badge
            status={record.isOnline ? 'success' : 'default'}
            offset={[-2, 2]}
          >
            <Avatar
              size={48}
              src={record.avatar}
              icon={<UserOutlined />}
              style={{ marginRight: 12 }}
            />
          </Badge>
          <div>
            <div style={{ fontWeight: 'bold', marginBottom: '4px' }}>
              {record.nickname || record.username}
              {record.role === 'admin' && (
                <CrownOutlined style={{ color: '#faad14', marginLeft: '4px' }} />
              )}
            </div>
            <div style={{ color: '#666', fontSize: '12px' }}>
              @{record.username}
            </div>
            <div style={{ color: '#666', fontSize: '12px' }}>
              {record.email}
            </div>
          </div>
        </div>
      )
    },
    {
      title: '角色',
      dataIndex: 'role',
      key: 'role',
      width: 100,
      render: (role) => {
        const roleConfig = {
          admin: { color: 'red', text: '管理员' },
          editor: { color: 'blue', text: '编辑' },
          author: { color: 'green', text: '作者' },
          user: { color: 'default', text: '用户' }
        };
        const config = roleConfig[role] || { color: 'default', text: role };
        return <Tag color={config.color}>{config.text}</Tag>;
      }
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status, record) => (
        <Switch
          checked={status === 'active'}
          checkedChildren="启用"
          unCheckedChildren="禁用"
          onChange={(checked) => {
            updateStatusMutation.mutate({
              id: record.id,
              status: checked ? 'active' : 'inactive'
            });
          }}
          loading={updateStatusMutation.isLoading}
        />
      )
    },
    {
      title: '统计',
      key: 'stats',
      width: 120,
      render: (_, record) => (
        <div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            文章：{record.articleCount || 0}
          </div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            评论：{record.commentCount || 0}
          </div>
        </div>
      )
    },
    {
      title: '注册时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 120,
      render: (date) => (
        <div>
          <div style={{ fontSize: '12px' }}>
            {formatDate(date, 'YYYY-MM-DD')}
          </div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            {formatDate(date, 'HH:mm')}
          </div>
        </div>
      )
    },
    {
      title: '最后登录',
      dataIndex: 'lastLoginAt',
      key: 'lastLoginAt',
      width: 120,
      render: (date) => (
        <div>
          {date ? (
            <>
              <div style={{ fontSize: '12px' }}>
                {formatDate(date, 'MM-DD')}
              </div>
              <div style={{ fontSize: '12px', color: '#666' }}>
                {formatDate(date, 'HH:mm')}
              </div>
            </>
          ) : (
            <span style={{ color: '#999' }}>从未登录</span>
          )}
        </div>
      )
    },
    {
      title: '操作',
      key: 'actions',
      width: 200,
      fixed: 'right',
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="编辑">
            <Button
              type="text"
              size="small"
              icon={<EditOutlined />}
              onClick={() => openUserModal('edit', record)}
            />
          </Tooltip>
          <Tooltip title="重置密码">
            <Popconfirm
              title="确定要重置该用户的密码吗？"
              onConfirm={() => resetPasswordMutation.mutate(record.id)}
              okText="确定"
              cancelText="取消"
            >
              <Button
                type="text"
                size="small"
                icon={record.status === 'active' ? <LockOutlined /> : <UnlockOutlined />}
                loading={resetPasswordMutation.isLoading}
              />
            </Popconfirm>
          </Tooltip>
          <Popconfirm
            title="确定要删除这个用户吗？"
            onConfirm={() => deleteMutation.mutate(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="text"
              size="small"
              danger
              icon={<DeleteOutlined />}
              loading={deleteMutation.isLoading}
              disabled={record.role === 'admin'}
            />
          </Popconfirm>
        </Space>
      )
    }
  ];
  
  // 行选择配置
  const rowSelection = {
    selectedRowKeys,
    onChange: setSelectedRowKeys,
    getCheckboxProps: (record) => ({
      disabled: record.role === 'admin'
    })
  };
  
  return (
    <div>
      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="总用户数"
              value={statsData?.total || 0}
              prefix={<TeamOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="活跃用户"
              value={statsData?.active || 0}
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="在线用户"
              value={statsData?.online || 0}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="本月新增"
              value={statsData?.monthlyNew || 0}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
      </Row>
      
      <Card>
        {/* 搜索表单 */}
        <Form
          form={searchForm}
          layout="inline"
          onFinish={handleSearch}
          style={{ marginBottom: 16 }}
        >
          <Form.Item name="keyword">
            <Search
              placeholder="搜索用户名、邮箱..."
              style={{ width: 200 }}
              onChange={(e) => setFilters(prev => ({ ...prev, keyword: e.target.value }))}
              allowClear
            />
          </Form.Item>
          <Form.Item name="role">
            <Select placeholder="角色" style={{ width: 120 }} allowClear>
              <Option value="admin">管理员</Option>
              <Option value="editor">编辑</Option>
              <Option value="author">作者</Option>
              <Option value="user">用户</Option>
            </Select>
          </Form.Item>
          <Form.Item name="status">
            <Select placeholder="状态" style={{ width: 120 }} allowClear>
              <Option value="active">启用</Option>
              <Option value="inactive">禁用</Option>
            </Select>
          </Form.Item>
          <Form.Item name="dateRange">
            <RangePicker placeholder={['开始日期', '结束日期']} />
          </Form.Item>
          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit" icon={<SearchOutlined />}>
                搜索
              </Button>
              <Button onClick={handleReset}>
                重置
              </Button>
            </Space>
          </Form.Item>
        </Form>
        
        <Divider />
        
        {/* 操作按钮 */}
        <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between' }}>
          <Space>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => openUserModal('create')}
            >
              新建用户
            </Button>
            {selectedRowKeys.length > 0 && (
              <Popconfirm
                title={`确定要删除选中的 ${selectedRowKeys.length} 个用户吗？`}
                onConfirm={() => batchDeleteMutation.mutate(selectedRowKeys)}
                okText="确定"
                cancelText="取消"
              >
                <Button
                  danger
                  icon={<DeleteOutlined />}
                  loading={batchDeleteMutation.isLoading}
                >
                  批量删除 ({selectedRowKeys.length})
                </Button>
              </Popconfirm>
            )}
          </Space>
          <Space>
            <Button icon={<ReloadOutlined />} onClick={() => refetchUsers()}>
              刷新
            </Button>
          </Space>
        </div>
        
        {/* 用户表格 */}
        <Table
          rowSelection={rowSelection}
          columns={columns}
          dataSource={userData?.data || []}
          loading={userLoading}
          rowKey="id"
          pagination={{
            ...pagination,
            total: userData?.total || 0,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
          }}
          onChange={handleTableChange}
          scroll={{ x: 1200 }}
        />
      </Card>
      
      {/* 用户编辑模态框 */}
      <Modal
        title={modalType === 'create' ? '新建用户' : '编辑用户'}
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false);
          userForm.resetFields();
        }}
        footer={null}
        width={600}
      >
        <Form
          form={userForm}
          layout="vertical"
          onFinish={handleUserSubmit}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="username"
                label="用户名"
                rules={[
                  { required: true, message: '请输入用户名' },
                  { min: 3, max: 20, message: '用户名长度为3-20个字符' }
                ]}
              >
                <Input prefix={<UserOutlined />} placeholder="请输入用户名" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="nickname"
                label="昵称"
                rules={[{ max: 50, message: '昵称不能超过50个字符' }]}
              >
                <Input placeholder="请输入昵称" />
              </Form.Item>
            </Col>
          </Row>
          
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="email"
                label="邮箱"
                rules={[
                  { required: true, message: '请输入邮箱' },
                  { type: 'email', message: '请输入有效的邮箱地址' }
                ]}
              >
                <Input prefix={<MailOutlined />} placeholder="请输入邮箱" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="phone"
                label="手机号"
                rules={[
                  { pattern: /^1[3-9]\d{9}$/, message: '请输入有效的手机号' }
                ]}
              >
                <Input prefix={<PhoneOutlined />} placeholder="请输入手机号" />
              </Form.Item>
            </Col>
          </Row>
          
          {modalType === 'create' && (
            <Form.Item
              name="password"
              label="密码"
              rules={[
                { required: true, message: '请输入密码' },
                { min: 6, message: '密码至少6个字符' }
              ]}
            >
              <Input.Password prefix={<LockOutlined />} placeholder="请输入密码" />
            </Form.Item>
          )}
          
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="role"
                label="角色"
                rules={[{ required: true, message: '请选择角色' }]}
              >
                <Select placeholder="请选择角色">
                  <Option value="user">用户</Option>
                  <Option value="author">作者</Option>
                  <Option value="editor">编辑</Option>
                  <Option value="admin">管理员</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="status"
                label="状态"
                rules={[{ required: true, message: '请选择状态' }]}
                initialValue="active"
              >
                <Select placeholder="请选择状态">
                  <Option value="active">启用</Option>
                  <Option value="inactive">禁用</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
          
          <Form.Item
            name="bio"
            label="个人简介"
            rules={[{ max: 200, message: '个人简介不能超过200个字符' }]}
          >
            <Input.TextArea rows={3} placeholder="请输入个人简介" />
          </Form.Item>
          
          <Form.Item style={{ textAlign: 'right', marginBottom: 0 }}>
            <Space>
              <Button onClick={() => setModalVisible(false)}>
                取消
              </Button>
              <Button
                type="primary"
                htmlType="submit"
                loading={createMutation.isLoading || updateMutation.isLoading}
              >
                {modalType === 'create' ? '创建' : '更新'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default UserManagement;