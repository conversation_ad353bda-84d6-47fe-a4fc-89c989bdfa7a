import express from 'express';
import {
  getArticles,
  getArticle,
  createArticle,
  updateArticle,
  deleteArticle,
  batchUpdateArticles as batchDeleteArticles,
  batchUpdateArticles as toggleArticleStatus,
  getArticleStats,
  getArticles as searchArticles,
  getRelatedArticles,
  getArticle as incrementViews,
  toggleLike,
  getPopularArticles,
  getArticles as getRecentArticles,
  getArticles as getArticlesByTag,
  getUserArticles as getArticlesByAuthor
} from '../controllers/articleController.js';
import {
  authenticate as authenticateToken,
  optionalAuth,
  authorize as requireRole,
  checkOwnership as checkResourceOwnership
} from '../middleware/auth.js';
import {
  validateArticleCreation as validateArticleCreate,
  validateArticleUpdate,
  validateObjectId,
  validatePagination,
  validateSearch,
  validateBatchOperation as validateBatchDelete
} from '../middleware/validation.js';

const router = express.Router();

// 公开路由
// 获取文章列表（支持分页、筛选、搜索）
router.get('/', validatePagination, getArticles);

// 搜索文章
router.get('/search', validateSearch, searchArticles);

// 获取热门文章
router.get('/popular', validatePagination, getPopularArticles);

// 获取最新文章
router.get('/recent', validatePagination, getRecentArticles);

// 按标签获取文章
router.get('/tag/:tag', validatePagination, getArticlesByTag);

// 按作者获取文章
router.get('/author/:authorId', validateObjectId('authorId'), validatePagination, getArticlesByAuthor);

// 获取单篇文章（支持可选认证以获取更多信息）
router.get('/:id', validateObjectId('id'), optionalAuth, getArticle);

// 获取相关文章
router.get('/:id/related', validateObjectId('id'), getRelatedArticles);

// 增加文章浏览量
router.post('/:id/view', validateObjectId('id'), incrementViews);

// 需要认证的路由
// 点赞/取消点赞文章
router.post('/:id/like', validateObjectId('id'), authenticateToken, toggleLike);

// 创建文章（需要编辑权限）
router.post('/', 
  authenticateToken, 
  requireRole(['admin', 'editor']), 
  validateArticleCreate, 
  createArticle
);

// 更新文章（需要是作者或管理员）
router.put('/:id', 
  validateObjectId('id'),
  authenticateToken,
  checkResourceOwnership('Article', ['admin']),
  validateArticleUpdate,
  updateArticle
);

// 切换文章状态（发布/草稿）
router.patch('/:id/status',
  validateObjectId('id'),
  authenticateToken,
  checkResourceOwnership('Article', ['admin']),
  toggleArticleStatus
);

// 删除文章（需要是作者或管理员）
router.delete('/:id',
  validateObjectId('id'),
  authenticateToken,
  checkResourceOwnership('Article', ['admin']),
  deleteArticle
);

// 管理员专用路由
// 批量删除文章
router.delete('/',
  authenticateToken,
  requireRole(['admin']),
  validateBatchDelete,
  batchDeleteArticles
);

// 获取文章统计信息
router.get('/admin/stats',
  authenticateToken,
  requireRole(['admin', 'editor']),
  getArticleStats
);

export default router;