import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 日志级别
const LOG_LEVELS = {
  ERROR: 0,
  WARN: 1,
  INFO: 2,
  DEBUG: 3
};

// 确保日志目录存在
const ensureLogDirectory = async () => {
  const logDir = path.join(process.cwd(), 'logs');
  try {
    await fs.access(logDir);
  } catch (error) {
    if (error.code === 'ENOENT') {
      await fs.mkdir(logDir, { recursive: true });
    }
  }
  return logDir;
};

// 格式化日志消息
const formatLogMessage = (level, message, meta = {}) => {
  const timestamp = new Date().toISOString();
  const logEntry = {
    timestamp,
    level,
    message,
    ...meta
  };
  return JSON.stringify(logEntry) + '\n';
};

// 写入日志文件
const writeLog = async (level, message, meta = {}) => {
  try {
    const logDir = await ensureLogDirectory();
    const logFile = path.join(logDir, `app-${new Date().toISOString().split('T')[0]}.log`);
    const logMessage = formatLogMessage(level, message, meta);
    
    await fs.appendFile(logFile, logMessage);
  } catch (error) {
    console.error('写入日志失败:', error);
  }
};

// 日志记录器
export const logger = {
  error: (message, meta = {}) => {
    console.error(`❌ [ERROR] ${message}`, meta);
    writeLog('ERROR', message, meta);
  },
  
  warn: (message, meta = {}) => {
    console.warn(`⚠️ [WARN] ${message}`, meta);
    writeLog('WARN', message, meta);
  },
  
  info: (message, meta = {}) => {
    console.log(`ℹ️ [INFO] ${message}`, meta);
    writeLog('INFO', message, meta);
  },
  
  debug: (message, meta = {}) => {
    if (process.env.NODE_ENV === 'development') {
      console.log(`🐛 [DEBUG] ${message}`, meta);
      writeLog('DEBUG', message, meta);
    }
  }
};

// 请求日志中间件
export const requestLogger = (req, res, next) => {
  const startTime = Date.now();
  const { method, url, ip, headers } = req;
  
  // 记录请求开始
  logger.info('请求开始', {
    method,
    url,
    ip,
    userAgent: headers['user-agent'],
    contentType: headers['content-type']
  });
  
  // 监听响应结束
  res.on('finish', () => {
    const duration = Date.now() - startTime;
    const { statusCode } = res;
    
    const logLevel = statusCode >= 400 ? 'error' : 'info';
    const message = `请求完成 ${method} ${url}`;
    
    logger[logLevel](message, {
      method,
      url,
      statusCode,
      duration: `${duration}ms`,
      ip,
      userAgent: headers['user-agent']
    });
  });
  
  next();
};

// 性能监控中间件
export const performanceMonitor = (req, res, next) => {
  const startTime = process.hrtime.bigint();
  const startMemory = process.memoryUsage();
  
  res.on('finish', () => {
    const endTime = process.hrtime.bigint();
    const endMemory = process.memoryUsage();
    
    const duration = Number(endTime - startTime) / 1000000; // 转换为毫秒
    const memoryDiff = {
      heapUsed: endMemory.heapUsed - startMemory.heapUsed,
      heapTotal: endMemory.heapTotal - startMemory.heapTotal,
      external: endMemory.external - startMemory.external
    };
    
    // 如果请求时间超过阈值，记录警告
    if (duration > 1000) { // 1秒
      logger.warn('慢请求检测', {
        url: req.url,
        method: req.method,
        duration: `${duration.toFixed(2)}ms`,
        memoryDiff
      });
    }
    
    // 如果内存使用增长过多，记录警告
    if (memoryDiff.heapUsed > 50 * 1024 * 1024) { // 50MB
      logger.warn('内存使用异常', {
        url: req.url,
        method: req.method,
        memoryDiff: {
          heapUsed: `${(memoryDiff.heapUsed / 1024 / 1024).toFixed(2)}MB`,
          heapTotal: `${(memoryDiff.heapTotal / 1024 / 1024).toFixed(2)}MB`
        }
      });
    }
  });
  
  next();
};

// 系统健康检查
export const systemHealthCheck = () => {
  const memoryUsage = process.memoryUsage();
  const cpuUsage = process.cpuUsage();
  
  return {
    uptime: process.uptime(),
    memory: {
      used: Math.round(memoryUsage.heapUsed / 1024 / 1024 * 100) / 100,
      total: Math.round(memoryUsage.heapTotal / 1024 / 1024 * 100) / 100,
      external: Math.round(memoryUsage.external / 1024 / 1024 * 100) / 100,
      rss: Math.round(memoryUsage.rss / 1024 / 1024 * 100) / 100
    },
    cpu: {
      user: cpuUsage.user,
      system: cpuUsage.system
    },
    loadAverage: process.platform !== 'win32' ? require('os').loadavg() : [0, 0, 0]
  };
};

// 定期系统监控
export const startSystemMonitoring = (interval = 60000) => { // 默认1分钟
  setInterval(() => {
    const health = systemHealthCheck();
    
    // 检查内存使用率
    const memoryUsagePercent = (health.memory.used / health.memory.total) * 100;
    if (memoryUsagePercent > 80) {
      logger.warn('内存使用率过高', {
        usage: `${memoryUsagePercent.toFixed(2)}%`,
        used: `${health.memory.used}MB`,
        total: `${health.memory.total}MB`
      });
    }
    
    // 记录系统状态（仅在开发环境）
    if (process.env.NODE_ENV === 'development') {
      logger.debug('系统状态', health);
    }
  }, interval);
};

// 清理旧日志文件
export const cleanupOldLogs = async (daysToKeep = 30) => {
  try {
    const logDir = await ensureLogDirectory();
    const files = await fs.readdir(logDir);
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);
    
    for (const file of files) {
      if (file.endsWith('.log')) {
        const filePath = path.join(logDir, file);
        const stats = await fs.stat(filePath);
        
        if (stats.mtime < cutoffDate) {
          await fs.unlink(filePath);
          logger.info('清理旧日志文件', { file });
        }
      }
    }
  } catch (error) {
    logger.error('清理日志文件失败', { error: error.message });
  }
};

export default {
  logger,
  requestLogger,
  performanceMonitor,
  systemHealthCheck,
  startSystemMonitoring,
  cleanupOldLogs
};