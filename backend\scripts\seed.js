import mongoose from 'mongoose';
import bcrypt from 'bcryptjs';
import dotenv from 'dotenv';
import User from '../models/User.js';
import DatabaseConfig from '../config/database.js';

// 加载环境变量
dotenv.config();

// 默认管理员配置
const DEFAULT_ADMIN = {
  username: process.env.ADMIN_USERNAME || 'admin',
  email: process.env.ADMIN_EMAIL || '<EMAIL>',
  password: process.env.ADMIN_PASSWORD || 'admin123456',
  displayName: '系统管理员',
  role: 'admin',
  bio: '墨影博客系统管理员',
  status: 'active'
};

// 创建管理员用户
const createAdminUser = async () => {
  try {
    console.log('🔍 检查管理员用户是否已存在...');
    
    // 检查是否已存在管理员用户
    const existingAdmin = await User.findOne({
      $or: [
        { username: DEFAULT_ADMIN.username },
        { email: DEFAULT_ADMIN.email },
        { role: 'admin' }
      ]
    });
    
    if (existingAdmin) {
      console.log('✅ 管理员用户已存在:');
      console.log(`   用户名: ${existingAdmin.username}`);
      console.log(`   邮箱: ${existingAdmin.email}`);
      console.log(`   角色: ${existingAdmin.role}`);
      console.log(`   状态: ${existingAdmin.status}`);
      return existingAdmin;
    }
    
    console.log('🚀 创建默认管理员用户...');
    
    // 创建新的管理员用户
    const adminUser = new User({
      username: DEFAULT_ADMIN.username,
      email: DEFAULT_ADMIN.email,
      password: DEFAULT_ADMIN.password,
      profile: {
        displayName: DEFAULT_ADMIN.displayName,
        bio: DEFAULT_ADMIN.bio
      },
      role: DEFAULT_ADMIN.role,
      status: DEFAULT_ADMIN.status
    });
    
    await adminUser.save();
    
    console.log('✅ 管理员用户创建成功!');
    console.log('📋 登录信息:');
    console.log(`   用户名: ${DEFAULT_ADMIN.username}`);
    console.log(`   邮箱: ${DEFAULT_ADMIN.email}`);
    console.log(`   密码: ${DEFAULT_ADMIN.password}`);
    console.log('⚠️  请及时修改默认密码!');
    
    return adminUser;
    
  } catch (error) {
    console.error('❌ 创建管理员用户失败:', error.message);
    throw error;
  }
};

// 创建示例数据
const createSampleData = async () => {
  try {
    console.log('\n📝 检查示例数据...');
    
    // 这里可以添加创建示例文章、图片等数据的逻辑
    // 暂时跳过，只创建管理员用户
    
    console.log('✅ 示例数据检查完成');
    
  } catch (error) {
    console.error('❌ 创建示例数据失败:', error.message);
    throw error;
  }
};

// 主函数
const seed = async () => {
  try {
    console.log('🌱 开始初始化数据库...');
    console.log('=' .repeat(50));
    
    // 连接数据库
    console.log('🔌 连接数据库...');
    const dbConfig = new DatabaseConfig();
    await dbConfig.connect();
    
    // 创建管理员用户
    await createAdminUser();
    
    // 创建示例数据
    await createSampleData();
    
    console.log('\n' + '=' .repeat(50));
    console.log('🎉 数据库初始化完成!');
    console.log('\n💡 提示:');
    console.log('   1. 请使用上述登录信息访问管理后台');
    console.log('   2. 登录后请立即修改默认密码');
    console.log('   3. 建议创建其他管理员账户作为备份');
    console.log('\n🌐 管理后台地址: http://localhost:3000');
    
  } catch (error) {
    console.error('\n❌ 数据库初始化失败:', error.message);
    
    // 提供解决建议
    if (error.message.includes('ECONNREFUSED')) {
      console.error('\n💡 解决建议:');
      console.error('   1. 确保 MongoDB 服务正在运行');
      console.error('   2. 检查数据库连接配置');
    } else if (error.message.includes('duplicate key')) {
      console.error('\n💡 解决建议:');
      console.error('   1. 管理员用户可能已存在');
      console.error('   2. 检查用户名或邮箱是否重复');
    }
    
    process.exit(1);
  } finally {
    // 关闭数据库连接
    await mongoose.connection.close();
    console.log('\n🔌 数据库连接已关闭');
    process.exit(0);
  }
};

// 运行种子脚本
seed();