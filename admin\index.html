<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>墨影博客管理后台</title>
    <meta name="description" content="墨影博客管理后台 - 现代化的博客内容管理系统" />
    <meta name="keywords" content="博客,管理后台,内容管理,CMS" />
    <meta name="author" content="MoYing Blog Team" />
    
    <!-- 预加载关键资源 -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    
    <!-- 防止FOUC -->
    <style>
      #root {
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      }
      
      .loading {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 16px;
        color: white;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      }
      
      .loading-spinner {
        width: 40px;
        height: 40px;
        border: 3px solid rgba(255, 255, 255, 0.3);
        border-top: 3px solid white;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      
      .loading-text {
        font-size: 16px;
        font-weight: 500;
      }
    </style>
  </head>
  <body>
    <div id="root">
      <div class="loading">
        <div class="loading-spinner"></div>
        <div class="loading-text">墨影博客管理后台加载中...</div>
      </div>
    </div>
    <script type="module" src="/src/main.jsx"></script>
  </body>
</html>