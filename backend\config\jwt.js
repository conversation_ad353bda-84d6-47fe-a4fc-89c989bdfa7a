import jwt from 'jsonwebtoken';
import dotenv from 'dotenv';

dotenv.config();

class JWTConfig {
  constructor() {
    this.secret = process.env.JWT_SECRET;
    this.expiresIn = process.env.JWT_EXPIRES_IN || '7d';
    this.refreshExpiresIn = process.env.JWT_REFRESH_EXPIRES_IN || '30d';
  }

  // 生成访问令牌
  generateAccessToken(payload) {
    return jwt.sign(payload, this.secret, {
      expiresIn: this.expiresIn,
      issuer: 'moying-blog',
      audience: 'moying-blog-users'
    });
  }

  // 生成刷新令牌
  generateRefreshToken(payload) {
    return jwt.sign(payload, this.secret, {
      expiresIn: this.refreshExpiresIn,
      issuer: 'moying-blog',
      audience: 'moying-blog-users'
    });
  }

  // 验证令牌
  verifyToken(token) {
    try {
      return jwt.verify(token, this.secret, {
        issuer: 'moying-blog',
        audience: 'moying-blog-users'
      });
    } catch (error) {
      throw new Error('无效的令牌');
    }
  }

  // 解码令牌（不验证）
  decodeToken(token) {
    return jwt.decode(token);
  }

  // 生成令牌对
  generateTokenPair(payload) {
    const accessToken = this.generateAccessToken(payload);
    const refreshToken = this.generateRefreshToken(payload);
    
    return {
      accessToken,
      refreshToken,
      expiresIn: this.expiresIn
    };
  }
}

export default new JWTConfig();