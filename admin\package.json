{"name": "moying-blog-admin", "version": "1.0.0", "description": "墨影博客管理后台", "type": "module", "main": "index.js", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["blog", "admin", "react", "vite"], "author": "MoYing Blog Team", "license": "MIT", "dependencies": {"@ant-design/icons": "^5.0.1", "@tanstack/react-query": "^5.87.1", "antd": "^5.3.0", "axios": "^1.3.4", "dayjs": "^1.11.7", "js-cookie": "^3.0.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.43.5", "react-markdown": "^8.0.5", "react-query": "^3.39.3", "react-router-dom": "^6.8.1", "react-syntax-highlighter": "^15.5.0", "socket.io-client": "^4.8.1", "zustand": "^4.3.6"}, "devDependencies": {"@types/react": "^18.0.28", "@types/react-dom": "^18.0.11", "@vitejs/plugin-react": "^3.1.0", "eslint": "^8.36.0", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.3.4", "vite": "^4.2.0"}}