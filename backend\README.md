# 墨影博客后端系统

一个现代化的博客后端系统，采用 Node.js + Express + MongoDB 技术栈构建。

## 🚀 技术栈

- **后端框架**: Express.js
- **数据库**: MongoDB + Mongoose
- **认证系统**: JWT (JSON Web Tokens)
- **API风格**: RESTful API
- **文件上传**: Multer + Sharp (图片处理)
- **安全**: Helmet, CORS, Rate Limiting
- **开发工具**: Nodemon, ESLint

## 📋 功能特性

### 🔐 用户认证
- 用户注册/登录
- JWT访问令牌 + 刷新令牌
- 密码加密存储
- 用户权限管理（用户/编辑/管理员）
- 账户状态管理

### 📝 文章管理
- 文章CRUD操作
- 文章状态管理（草稿/发布）
- 文章搜索和筛选
- 标签系统
- 浏览量统计
- 点赞功能

### 🖼️ 图片管理
- 图片上传和处理
- 多尺寸图片生成
- 图片引用管理
- 存储统计
- 未使用图片清理

### 👥 用户管理
- 用户列表和搜索
- 用户状态切换
- 批量操作
- 用户统计信息
- 活动日志

## 🛠️ 安装和运行

### 环境要求
- Node.js >= 16.0.0
- MongoDB >= 4.4
- npm >= 8.0.0

### 1. 克隆项目
```bash
cd backend
```

### 2. 安装依赖
```bash
npm install
```

### 3. 环境配置
复制环境变量模板文件：
```bash
cp .env.example .env
```

编辑 `.env` 文件，配置以下变量：
```env
# 服务器配置
PORT=5000
NODE_ENV=development

# 数据库配置
MONGODB_URI=mongodb://localhost:27017/moying-blog
MONGODB_URI_TEST=mongodb://localhost:27017/moying-blog-test

# JWT配置
JWT_SECRET=your-super-secret-jwt-key-here
JWT_REFRESH_SECRET=your-super-secret-refresh-key-here
JWT_EXPIRES_IN=15m
JWT_REFRESH_EXPIRES_IN=7d

# 文件上传配置
UPLOAD_PATH=./uploads
MAX_FILE_SIZE=5242880
ALLOWED_IMAGE_TYPES=image/jpeg,image/png,image/webp

# 安全配置
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW=15
RATE_LIMIT_MAX=100
```

### 4. 启动MongoDB
确保MongoDB服务正在运行：
```bash
# Windows
net start MongoDB

# macOS (使用Homebrew)
brew services start mongodb-community

# Linux
sudo systemctl start mongod
```

### 5. 运行项目

开发模式：
```bash
npm run dev
```

生产模式：
```bash
npm start
```

运行测试：
```bash
npm test
```

## 📚 API文档

### 基础信息
- **Base URL**: `http://localhost:5000/api`
- **Content-Type**: `application/json`
- **认证方式**: Bearer Token

### 主要端点

#### 🔐 认证相关 (`/api/auth`)
```
POST   /register              # 用户注册
POST   /login                 # 用户登录
POST   /refresh               # 刷新令牌
POST   /logout                # 用户登出
GET    /profile               # 获取用户资料
PUT    /profile               # 更新用户资料
PUT    /password              # 修改密码
DELETE /account               # 删除账户
GET    /check-username/:username  # 检查用户名可用性
GET    /check-email/:email    # 检查邮箱可用性
```

#### 📝 文章管理 (`/api/articles`)
```
GET    /                      # 获取文章列表
GET    /search                # 搜索文章
GET    /popular               # 获取热门文章
GET    /recent                # 获取最新文章
GET    /:id                   # 获取单篇文章
POST   /                      # 创建文章
PUT    /:id                   # 更新文章
DELETE /:id                   # 删除文章
POST   /:id/like              # 点赞文章
POST   /:id/view              # 增加浏览量
```

#### 🖼️ 图片管理 (`/api/images`)
```
POST   /upload                # 上传单张图片
POST   /upload/batch          # 批量上传图片
GET    /                      # 获取图片列表
GET    /:id                   # 获取图片信息
PUT    /:id                   # 更新图片信息
DELETE /:id                   # 删除图片
POST   /:id/reference         # 添加图片引用
DELETE /:id/reference         # 移除图片引用
```

#### 👥 用户管理 (`/api/users`) - 管理员专用
```
GET    /                      # 获取用户列表
GET    /search                # 搜索用户
GET    /stats                 # 获取用户统计
GET    /:id                   # 获取用户信息
POST   /                      # 创建用户
PUT    /:id                   # 更新用户信息
DELETE /:id                   # 删除用户
PATCH  /:id/status            # 切换用户状态
```

### 请求示例

#### 用户注册
```bash
curl -X POST http://localhost:5000/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "email": "<EMAIL>",
    "password": "password123",
    "displayName": "测试用户"
  }'
```

#### 用户登录
```bash
curl -X POST http://localhost:5000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "password": "password123"
  }'
```

#### 创建文章
```bash
curl -X POST http://localhost:5000/api/articles \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -d '{
    "title": "我的第一篇文章",
    "content": "这是文章内容...",
    "tags": ["技术", "博客"],
    "status": "published"
  }'
```

## 🗂️ 项目结构

```
backend/
├── config/                 # 配置文件
│   ├── database.js        # 数据库配置
│   └── jwt.js             # JWT配置
├── controllers/           # 控制器
│   ├── authController.js  # 认证控制器
│   ├── articleController.js # 文章控制器
│   ├── imageController.js # 图片控制器
│   └── userController.js  # 用户控制器
├── middleware/            # 中间件
│   ├── auth.js           # 认证中间件
│   ├── errorHandler.js   # 错误处理中间件
│   ├── upload.js         # 文件上传中间件
│   └── validation.js     # 数据验证中间件
├── models/               # 数据模型
│   ├── User.js          # 用户模型
│   ├── Article.js       # 文章模型
│   └── Image.js         # 图片模型
├── routes/               # 路由
│   ├── auth.js          # 认证路由
│   ├── articles.js      # 文章路由
│   ├── images.js        # 图片路由
│   ├── users.js         # 用户路由
│   └── index.js         # 路由入口
├── uploads/              # 上传文件目录
├── app.js               # 应用配置
├── server.js            # 服务器启动文件
├── package.json         # 项目配置
├── .env.example         # 环境变量模板
└── README.md           # 项目说明
```

## 🔒 安全特性

- **密码加密**: 使用bcrypt进行密码哈希
- **JWT认证**: 访问令牌 + 刷新令牌机制
- **请求限制**: 防止暴力攻击和DDoS
- **CORS配置**: 跨域请求安全控制
- **输入验证**: 严格的数据验证和清理
- **安全头**: 使用Helmet设置安全HTTP头
- **文件上传**: 文件类型和大小限制

## 🚀 部署指南

### 使用PM2部署

1. 安装PM2：
```bash
npm install -g pm2
```

2. 创建PM2配置文件 `ecosystem.config.js`：
```javascript
module.exports = {
  apps: [{
    name: 'moying-blog-backend',
    script: 'server.js',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 5000
    }
  }]
};
```

3. 启动应用：
```bash
pm2 start ecosystem.config.js
```

### 使用Docker部署

1. 创建 `Dockerfile`：
```dockerfile
FROM node:18-alpine

WORKDIR /app

COPY package*.json ./
RUN npm ci --only=production

COPY . .

EXPOSE 5000

USER node

CMD ["npm", "start"]
```

2. 构建和运行：
```bash
docker build -t moying-blog-backend .
docker run -p 5000:5000 moying-blog-backend
```

## 🧪 测试

运行所有测试：
```bash
npm test
```

运行测试并生成覆盖率报告：
```bash
npm run test:coverage
```

## 📈 性能优化

- **数据库索引**: 为常用查询字段添加索引
- **响应压缩**: 使用gzip压缩响应数据
- **缓存策略**: 静态资源缓存
- **连接池**: MongoDB连接池优化
- **图片优化**: 自动生成多尺寸图片

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🆘 常见问题

### Q: 启动时提示数据库连接失败？
A: 请检查MongoDB是否正在运行，以及 `.env` 文件中的数据库连接字符串是否正确。

### Q: 图片上传失败？
A: 请检查 `uploads` 目录是否存在且有写入权限，以及上传的文件是否符合大小和格式要求。

### Q: JWT令牌验证失败？
A: 请检查 `.env` 文件中的JWT密钥配置，确保前后端使用相同的密钥。

### Q: 如何重置管理员密码？
A: 可以直接在数据库中修改用户密码字段，或使用管理员重置密码API。

## 📞 联系方式

如有问题或建议，请通过以下方式联系：

- 项目Issues: [GitHub Issues](https://github.com/your-repo/issues)
- 邮箱: <EMAIL>

---

**墨影博客后端系统** - 让写作更简单，让分享更美好！ ✨