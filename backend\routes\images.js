import express from 'express';
import {
  uploadImage,
  uploadMultipleImages,
  getImages,
  getImage,
  updateImage,
  deleteImage,
  batchDeleteImages,
  addImageReference,
  removeImageReference,
  getImageStats,
  cleanupUnusedImages
} from '../controllers/imageController.js';
import {
  authenticate as authenticateToken,
  authorize as requireRole,
  checkOwnership as checkResourceOwnership
} from '../middleware/auth.js';
import {
  validateObjectId,
  validatePagination,
  validateImageUpload as validateImageUpdate,
  validateBatchOperation as validateBatchDelete,
  validateObjectId as validateImageReference
} from '../middleware/validation.js';
import {
  uploadSingle,
  uploadMultiple
} from '../middleware/upload.js';

const router = express.Router();

// 需要认证的路由
// 上传单张图片
router.post('/upload',
  authenticateToken,
  uploadSingle,
  uploadImage
);

// 批量上传图片
router.post('/upload/batch',
  authenticateToken,
  uploadMultiple,
  uploadMultipleImages
);

// 获取图片列表（支持分页、筛选）
router.get('/',
  authenticateToken,
  validatePagination,
  getImages
);

// 获取单张图片信息
router.get('/:id',
  validateObjectId('id'),
  authenticateToken,
  getImage
);

// 更新图片信息
router.put('/:id',
  validateObjectId('id'),
  authenticateToken,
  checkResourceOwnership('Image', ['admin']),
  validateImageUpdate,
  updateImage
);

// 删除图片
router.delete('/:id',
  validateObjectId('id'),
  authenticateToken,
  checkResourceOwnership('Image', ['admin']),
  deleteImage
);

// 添加图片引用
router.post('/:id/reference',
  validateObjectId('id'),
  authenticateToken,
  validateImageReference,
  addImageReference
);

// 移除图片引用
router.delete('/:id/reference',
  validateObjectId('id'),
  authenticateToken,
  validateImageReference,
  removeImageReference
);

// 管理员专用路由
// 批量删除图片
router.delete('/',
  authenticateToken,
  requireRole(['admin']),
  validateBatchDelete,
  batchDeleteImages
);

// 获取图片统计信息
router.get('/admin/stats',
  authenticateToken,
  requireRole(['admin', 'editor']),
  getImageStats
);

// 清理未使用的图片
router.post('/admin/cleanup',
  authenticateToken,
  requireRole(['admin']),
  cleanupUnusedImages
);

export default router;