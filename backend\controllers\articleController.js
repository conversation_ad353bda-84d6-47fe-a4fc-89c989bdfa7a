import Article from '../models/Article.js';
import { AppError, catchAsync } from '../middleware/errorHandler.js';
import websocketService from '../services/websocketService.js';
import mongoose from 'mongoose';

// 获取文章列表
export const getArticles = catchAsync(async (req, res, next) => {
  const {
    page = 1,
    limit = 10,
    sort = '-createdAt',
    status = 'published',
    category,
    tags,
    featured,
    author,
    q // 搜索关键词
  } = req.query;
  
  // 构建查询条件
  const query = {};
  
  // 状态过滤
  if (status && status !== 'all') {
    query.status = status;
  }
  
  // 分类过滤
  if (category) {
    query.category = category;
  }
  
  // 标签过滤
  if (tags) {
    const tagArray = Array.isArray(tags) ? tags : tags.split(',');
    query.tags = { $in: tagArray };
  }
  
  // 推荐过滤
  if (featured !== undefined) {
    query.featured = featured === 'true';
  }
  
  // 作者过滤
  if (author) {
    query.author = author;
  }
  
  // 搜索功能
  if (q) {
    query.$or = [
      { title: { $regex: q, $options: 'i' } },
      { content: { $regex: q, $options: 'i' } },
      { excerpt: { $regex: q, $options: 'i' } },
      { tags: { $in: [new RegExp(q, 'i')] } }
    ];
  }
  
  // 如果不是管理员或编辑，只能看到已发布的文章
  if (!req.user || !['admin', 'editor'].includes(req.user.role)) {
    query.status = 'published';
  }
  
  // 计算分页
  const skip = (parseInt(page) - 1) * parseInt(limit);
  
  // 执行查询
  const [articles, total] = await Promise.all([
    Article.find(query)
      .populate('author', 'username displayName avatar')
      .sort(sort)
      .skip(skip)
      .limit(parseInt(limit))
      .lean(),
    Article.countDocuments(query)
  ]);
  
  // 计算分页信息
  const totalPages = Math.ceil(total / parseInt(limit));
  const hasNextPage = parseInt(page) < totalPages;
  const hasPrevPage = parseInt(page) > 1;
  
  res.status(200).json({
    success: true,
    data: {
      articles,
      pagination: {
        currentPage: parseInt(page),
        totalPages,
        totalItems: total,
        itemsPerPage: parseInt(limit),
        hasNextPage,
        hasPrevPage
      }
    }
  });
});

// 获取单篇文章
export const getArticle = catchAsync(async (req, res, next) => {
  const { id } = req.params;
  
  // 支持通过ID或slug查询
  const query = mongoose.Types.ObjectId.isValid(id) 
    ? { _id: id } 
    : { slug: id };
  
  const article = await Article.findOne(query)
    .populate('author', 'username displayName avatar bio')
    .lean();
  
  if (!article) {
    return next(new AppError('文章不存在', 404));
  }
  
  // 检查访问权限
  if (article.status !== 'published') {
    if (!req.user || 
        (req.user._id.toString() !== article.author._id.toString() && 
         !['admin', 'editor'].includes(req.user.role))) {
      return next(new AppError('文章不存在', 404));
    }
  }
  
  // 增加浏览量（异步执行，不影响响应）
  if (article.status === 'published') {
    Article.findByIdAndUpdate(article._id, { $inc: { views: 1 } }).exec();
  }
  
  res.status(200).json({
    success: true,
    data: {
      article
    }
  });
});

// 创建文章
export const createArticle = catchAsync(async (req, res, next) => {
  const articleData = {
    ...req.body,
    author: req.user._id
  };
  
  // 如果没有提供slug，会在模型中自动生成
  const article = await Article.create(articleData);
  
  // 填充作者信息
  await article.populate('author', 'username displayName avatar');
  
  // 广播文章创建事件
  websocketService.broadcastArticleChange('created', {
    id: article._id,
    title: article.title,
    slug: article.slug,
    status: article.status,
    author: article.author
  });
  
  res.status(201).json({
    success: true,
    message: '文章创建成功',
    data: {
      article
    }
  });
});

// 更新文章
export const updateArticle = catchAsync(async (req, res, next) => {
  const { id } = req.params;
  
  const article = await Article.findById(id);
  
  if (!article) {
    return next(new AppError('文章不存在', 404));
  }
  
  // 检查权限
  if (req.user._id.toString() !== article.author.toString() && 
      !['admin', 'editor'].includes(req.user.role)) {
    return next(new AppError('没有权限修改此文章', 403));
  }
  
  // 更新文章
  const updatedArticle = await Article.findByIdAndUpdate(
    id,
    { ...req.body, updatedAt: new Date() },
    { new: true, runValidators: true }
  ).populate('author', 'username displayName avatar');
  
  // 广播文章更新事件
  websocketService.broadcastArticleChange('updated', {
    id: updatedArticle._id,
    title: updatedArticle.title,
    slug: updatedArticle.slug,
    status: updatedArticle.status,
    changes: req.body,
    author: updatedArticle.author
  });
  
  res.status(200).json({
    success: true,
    message: '文章更新成功',
    data: {
      article: updatedArticle
    }
  });
});

// 删除文章
export const deleteArticle = catchAsync(async (req, res, next) => {
  const { id } = req.params;
  
  const article = await Article.findById(id);
  
  if (!article) {
    return next(new AppError('文章不存在', 404));
  }
  
  // 检查权限
  if (req.user._id.toString() !== article.author.toString() && 
      req.user.role !== 'admin') {
    return next(new AppError('没有权限删除此文章', 403));
  }
  
  await Article.findByIdAndDelete(id);
  
  // 广播文章删除事件
  websocketService.broadcastArticleChange('deleted', {
    id: article._id,
    title: article.title,
    slug: article.slug
  });
  
  res.status(200).json({
    success: true,
    message: '文章删除成功'
  });
});

// 批量操作文章
export const batchUpdateArticles = catchAsync(async (req, res, next) => {
  const { ids, action } = req.body;
  
  if (!Array.isArray(ids) || ids.length === 0) {
    return next(new AppError('请提供要操作的文章ID列表', 400));
  }
  
  // 检查权限
  const articles = await Article.find({ _id: { $in: ids } });
  
  if (!req.user || req.user.role !== 'admin') {
    // 非管理员只能操作自己的文章
    const unauthorizedArticles = articles.filter(
      article => article.author.toString() !== req.user._id.toString()
    );
    
    if (unauthorizedArticles.length > 0) {
      return next(new AppError('没有权限操作某些文章', 403));
    }
  }
  
  let updateData = {};
  let message = '';
  
  switch (action) {
    case 'publish':
      updateData = { status: 'published', publishedAt: new Date() };
      message = '文章发布成功';
      break;
    case 'unpublish':
      updateData = { status: 'draft' };
      message = '文章取消发布成功';
      break;
    case 'archive':
      updateData = { status: 'archived' };
      message = '文章归档成功';
      break;
    case 'feature':
      updateData = { featured: true };
      message = '文章设为推荐成功';
      break;
    case 'unfeature':
      updateData = { featured: false };
      message = '文章取消推荐成功';
      break;
    case 'delete':
      await Article.deleteMany({ _id: { $in: ids } });
      return res.status(200).json({
        success: true,
        message: '文章删除成功',
        data: {
          deletedCount: ids.length
        }
      });
    default:
      return next(new AppError('无效的操作类型', 400));
  }
  
  const result = await Article.updateMany(
    { _id: { $in: ids } },
    updateData
  );
  
  res.status(200).json({
    success: true,
    message,
    data: {
      modifiedCount: result.modifiedCount
    }
  });
});

// 获取热门文章
export const getPopularArticles = catchAsync(async (req, res, next) => {
  const { limit = 10 } = req.query;
  
  const articles = await Article.getPopularArticles(parseInt(limit));
  
  res.status(200).json({
    success: true,
    data: {
      articles
    }
  });
});

// 获取相关文章
export const getRelatedArticles = catchAsync(async (req, res, next) => {
  const { id } = req.params;
  const { limit = 5 } = req.query;
  
  const article = await Article.findById(id);
  
  if (!article) {
    return next(new AppError('文章不存在', 404));
  }
  
  const relatedArticles = await Article.getRelatedArticles(
    article._id,
    article.tags,
    article.category,
    parseInt(limit)
  );
  
  res.status(200).json({
    success: true,
    data: {
      articles: relatedArticles
    }
  });
});

// 切换文章点赞
export const toggleLike = catchAsync(async (req, res, next) => {
  const { id } = req.params;
  
  const article = await Article.findById(id);
  
  if (!article) {
    return next(new AppError('文章不存在', 404));
  }
  
  if (article.status !== 'published') {
    return next(new AppError('只能点赞已发布的文章', 400));
  }
  
  const result = await article.toggleLike(req.user._id);
  
  res.status(200).json({
    success: true,
    message: result.liked ? '点赞成功' : '取消点赞成功',
    data: {
      liked: result.liked,
      likesCount: result.likesCount
    }
  });
});

// 获取文章统计信息
export const getArticleStats = catchAsync(async (req, res, next) => {
  const stats = await Article.aggregate([
    {
      $group: {
        _id: null,
        totalArticles: { $sum: 1 },
        publishedArticles: {
          $sum: { $cond: [{ $eq: ['$status', 'published'] }, 1, 0] }
        },
        draftArticles: {
          $sum: { $cond: [{ $eq: ['$status', 'draft'] }, 1, 0] }
        },
        archivedArticles: {
          $sum: { $cond: [{ $eq: ['$status', 'archived'] }, 1, 0] }
        },
        featuredArticles: {
          $sum: { $cond: ['$featured', 1, 0] }
        },
        totalViews: { $sum: '$views' },
        totalLikes: { $sum: { $size: '$likes' } },
        averageReadingTime: { $avg: '$readingTime' }
      }
    }
  ]);
  
  // 获取分类统计
  const categoryStats = await Article.aggregate([
    { $match: { status: 'published' } },
    { $group: { _id: '$category', count: { $sum: 1 } } },
    { $sort: { count: -1 } },
    { $limit: 10 }
  ]);
  
  // 获取标签统计
  const tagStats = await Article.aggregate([
    { $match: { status: 'published' } },
    { $unwind: '$tags' },
    { $group: { _id: '$tags', count: { $sum: 1 } } },
    { $sort: { count: -1 } },
    { $limit: 20 }
  ]);
  
  res.status(200).json({
    success: true,
    data: {
      overview: stats[0] || {
        totalArticles: 0,
        publishedArticles: 0,
        draftArticles: 0,
        archivedArticles: 0,
        featuredArticles: 0,
        totalViews: 0,
        totalLikes: 0,
        averageReadingTime: 0
      },
      categories: categoryStats,
      tags: tagStats
    }
  });
});

// 获取用户的文章
export const getUserArticles = catchAsync(async (req, res, next) => {
  const { userId } = req.params;
  const {
    page = 1,
    limit = 10,
    sort = '-createdAt',
    status = 'published'
  } = req.query;
  
  // 构建查询条件
  const query = { author: userId };
  
  // 如果不是文章作者本人或管理员，只能看到已发布的文章
  if (!req.user || 
      (req.user._id.toString() !== userId && !['admin', 'editor'].includes(req.user.role))) {
    query.status = 'published';
  } else if (status && status !== 'all') {
    query.status = status;
  }
  
  const skip = (parseInt(page) - 1) * parseInt(limit);
  
  const [articles, total] = await Promise.all([
    Article.find(query)
      .populate('author', 'username displayName avatar')
      .sort(sort)
      .skip(skip)
      .limit(parseInt(limit))
      .lean(),
    Article.countDocuments(query)
  ]);
  
  const totalPages = Math.ceil(total / parseInt(limit));
  
  res.status(200).json({
    success: true,
    data: {
      articles,
      pagination: {
        currentPage: parseInt(page),
        totalPages,
        totalItems: total,
        itemsPerPage: parseInt(limit),
        hasNextPage: parseInt(page) < totalPages,
        hasPrevPage: parseInt(page) > 1
      }
    }
  });
});

// 导出所有文章控制器
export default {
  getArticles,
  getArticle,
  createArticle,
  updateArticle,
  deleteArticle,
  batchUpdateArticles,
  getPopularArticles,
  getRelatedArticles,
  toggleLike,
  getArticleStats,
  getUserArticles
};