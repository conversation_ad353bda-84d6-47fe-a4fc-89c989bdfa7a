import mongoose from 'mongoose';

const imageSchema = new mongoose.Schema({
  filename: {
    type: String,
    required: [true, '文件名是必需的'],
    unique: true
  },
  originalName: {
    type: String,
    required: [true, '原始文件名是必需的']
  },
  path: {
    type: String,
    required: [true, '文件路径是必需的']
  },
  url: {
    type: String,
    required: [true, '访问URL是必需的']
  },
  mimeType: {
    type: String,
    required: [true, '文件类型是必需的'],
    enum: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp']
  },
  size: {
    type: Number,
    required: [true, '文件大小是必需的']
  },
  dimensions: {
    width: {
      type: Number,
      required: true
    },
    height: {
      type: Number,
      required: true
    }
  },
  metadata: {
    alt: {
      type: String,
      trim: true,
      maxlength: [200, 'Alt文本不能超过200个字符']
    },
    caption: {
      type: String,
      trim: true,
      maxlength: [500, '图片说明不能超过500个字符']
    },
    description: {
      type: String,
      trim: true,
      maxlength: [1000, '图片描述不能超过1000个字符']
    },
    tags: [{
      type: String,
      trim: true,
      maxlength: [30, '标签不能超过30个字符']
    }]
  },
  uploader: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, '上传者是必需的']
  },
  usage: {
    type: String,
    enum: ['article', 'cover', 'avatar', 'general'],
    default: 'general'
  },
  status: {
    type: String,
    enum: ['active', 'deleted'],
    default: 'active'
  },
  references: [{
    model: {
      type: String,
      enum: ['Article', 'User']
    },
    documentId: {
      type: mongoose.Schema.Types.ObjectId
    },
    field: {
      type: String // 引用的字段名，如 'coverImage', 'profile.avatar'
    }
  }],
  optimization: {
    compressed: {
      type: Boolean,
      default: false
    },
    thumbnails: [{
      size: String, // 如 '150x150', '300x200'
      path: String,
      url: String
    }],
    webpVersion: {
      path: String,
      url: String,
      size: Number
    }
  },
  exif: {
    camera: String,
    lens: String,
    settings: {
      iso: Number,
      aperture: String,
      shutterSpeed: String,
      focalLength: String
    },
    location: {
      latitude: Number,
      longitude: Number,
      address: String
    },
    dateTaken: Date
  }
}, {
  timestamps: true,
  toJSON: {
    transform: function(doc, ret) {
      delete ret.__v;
      return ret;
    }
  }
});

// 索引
imageSchema.index({ filename: 1 });
imageSchema.index({ uploader: 1, createdAt: -1 });
imageSchema.index({ usage: 1, status: 1 });
imageSchema.index({ 'metadata.tags': 1 });
imageSchema.index({ mimeType: 1 });
imageSchema.index({ size: 1 });

// 虚拟字段：文件大小（人类可读）
imageSchema.virtual('humanSize').get(function() {
  const bytes = this.size;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  if (bytes === 0) return '0 Bytes';
  const i = Math.floor(Math.log(bytes) / Math.log(1024));
  return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
});

// 虚拟字段：宽高比
imageSchema.virtual('aspectRatio').get(function() {
  if (!this.dimensions.width || !this.dimensions.height) return null;
  return (this.dimensions.width / this.dimensions.height).toFixed(2);
});

// 虚拟字段：是否为横向图片
imageSchema.virtual('isLandscape').get(function() {
  return this.dimensions.width > this.dimensions.height;
});

// 虚拟字段：是否为正方形图片
imageSchema.virtual('isSquare').get(function() {
  return this.dimensions.width === this.dimensions.height;
});

// 中间件：删除前清理文件
imageSchema.pre('deleteOne', { document: true, query: false }, async function() {
  const fs = await import('fs/promises');
  const path = await import('path');
  
  try {
    // 删除原图
    await fs.unlink(this.path);
    
    // 删除缩略图
    if (this.optimization.thumbnails) {
      for (const thumb of this.optimization.thumbnails) {
        try {
          await fs.unlink(thumb.path);
        } catch (error) {
          console.warn(`缩略图删除失败: ${thumb.path}`);
        }
      }
    }
    
    // 删除WebP版本
    if (this.optimization.webpVersion) {
      try {
        await fs.unlink(this.optimization.webpVersion.path);
      } catch (error) {
        console.warn(`WebP版本删除失败: ${this.optimization.webpVersion.path}`);
      }
    }
  } catch (error) {
    console.error('文件删除失败:', error);
  }
});

// 静态方法：按使用类型查找图片
imageSchema.statics.findByUsage = function(usage, options = {}) {
  const { page = 1, limit = 20, uploader } = options;
  const query = { usage, status: 'active' };
  
  if (uploader) query.uploader = uploader;
  
  const skip = (page - 1) * limit;
  
  return this.find(query)
    .populate('uploader', 'username profile.displayName')
    .sort({ createdAt: -1 })
    .skip(skip)
    .limit(limit);
};

// 静态方法：搜索图片
imageSchema.statics.search = function(query, options = {}) {
  const { page = 1, limit = 20, usage, uploader } = options;
  
  const searchQuery = {
    status: 'active',
    $or: [
      { originalName: { $regex: query, $options: 'i' } },
      { 'metadata.alt': { $regex: query, $options: 'i' } },
      { 'metadata.caption': { $regex: query, $options: 'i' } },
      { 'metadata.tags': { $regex: query, $options: 'i' } }
    ]
  };
  
  if (usage) searchQuery.usage = usage;
  if (uploader) searchQuery.uploader = uploader;
  
  const skip = (page - 1) * limit;
  
  return this.find(searchQuery)
    .populate('uploader', 'username profile.displayName')
    .sort({ createdAt: -1 })
    .skip(skip)
    .limit(limit);
};

// 静态方法：获取存储统计
imageSchema.statics.getStorageStats = async function(uploader = null) {
  const matchStage = { status: 'active' };
  if (uploader) matchStage.uploader = uploader;
  
  const stats = await this.aggregate([
    { $match: matchStage },
    {
      $group: {
        _id: null,
        totalImages: { $sum: 1 },
        totalSize: { $sum: '$size' },
        avgSize: { $avg: '$size' },
        byUsage: {
          $push: {
            usage: '$usage',
            size: '$size'
          }
        }
      }
    }
  ]);
  
  return stats[0] || {
    totalImages: 0,
    totalSize: 0,
    avgSize: 0,
    byUsage: []
  };
};

// 实例方法：添加引用
imageSchema.methods.addReference = function(model, documentId, field) {
  const existingRef = this.references.find(
    ref => ref.model === model && 
           ref.documentId.toString() === documentId.toString() && 
           ref.field === field
  );
  
  if (!existingRef) {
    this.references.push({ model, documentId, field });
    return this.save();
  }
  
  return Promise.resolve(this);
};

// 实例方法：移除引用
imageSchema.methods.removeReference = function(model, documentId, field) {
  this.references = this.references.filter(
    ref => !(ref.model === model && 
             ref.documentId.toString() === documentId.toString() && 
             ref.field === field)
  );
  return this.save();
};

// 实例方法：检查是否可以删除
imageSchema.methods.canDelete = function() {
  return this.references.length === 0;
};

const Image = mongoose.model('Image', imageSchema);

export default Image;