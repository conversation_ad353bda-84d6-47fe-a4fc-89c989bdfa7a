import { Server } from 'socket.io';
import jwt from 'jsonwebtoken';
import jwtConfig from '../config/jwt.js';

class WebSocketService {
  constructor() {
    this.io = null;
    this.connectedClients = new Map();
  }

  // 初始化WebSocket服务
  initialize(server) {
    this.io = new Server(server, {
      cors: {
        origin: process.env.NODE_ENV === 'production' 
          ? [process.env.FRONTEND_URL, process.env.ADMIN_URL]
          : ['http://localhost:3000', 'http://localhost:3001'],
        methods: ['GET', 'POST'],
        credentials: true
      },
      transports: ['websocket', 'polling']
    });

    // 中间件：身份验证
    this.io.use(async (socket, next) => {
      try {
        const token = socket.handshake.auth.token || socket.handshake.headers.authorization?.replace('Bearer ', '');
        
        if (!token) {
          // 允许匿名连接（前端展示）
          socket.user = { type: 'anonymous', id: socket.id };
          return next();
        }

        // 验证JWT token
        const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key');
        socket.user = { ...decoded, type: 'authenticated' };
        next();
      } catch (error) {
        console.error('WebSocket认证失败:', error.message);
        // 即使认证失败也允许连接，但标记为匿名用户
        socket.user = { type: 'anonymous', id: socket.id };
        next();
      }
    });

    // 连接处理
    this.io.on('connection', (socket) => {
      this.handleConnection(socket);
    });

    console.log('✅ WebSocket服务已启动');
  }

  // 处理客户端连接
  handleConnection(socket) {
    const clientInfo = {
      id: socket.id,
      user: socket.user,
      connectedAt: new Date(),
      rooms: new Set()
    };

    this.connectedClients.set(socket.id, clientInfo);
    
    console.log(`🔗 客户端连接: ${socket.id} (${socket.user.type})`);

    // 根据用户类型加入不同房间
    if (socket.user.type === 'authenticated') {
      socket.join('admin');
      clientInfo.rooms.add('admin');
      console.log(`👤 管理员加入admin房间: ${socket.id}`);
    } else {
      socket.join('frontend');
      clientInfo.rooms.add('frontend');
      console.log(`👁️ 访客加入frontend房间: ${socket.id}`);
    }

    // 发送连接确认
    socket.emit('connected', {
      success: true,
      clientId: socket.id,
      userType: socket.user.type,
      timestamp: new Date().toISOString()
    });

    // 监听客户端事件
    this.setupEventListeners(socket);

    // 处理断开连接
    socket.on('disconnect', (reason) => {
      console.log(`🔌 客户端断开: ${socket.id} (原因: ${reason})`);
      this.connectedClients.delete(socket.id);
    });
  }

  // 设置事件监听器
  setupEventListeners(socket) {
    // 心跳检测
    socket.on('ping', () => {
      socket.emit('pong', { timestamp: new Date().toISOString() });
    });

    // 加入特定房间
    socket.on('join-room', (roomName) => {
      if (this.isValidRoom(roomName)) {
        socket.join(roomName);
        const clientInfo = this.connectedClients.get(socket.id);
        if (clientInfo) {
          clientInfo.rooms.add(roomName);
        }
        socket.emit('room-joined', { room: roomName });
        console.log(`📍 客户端 ${socket.id} 加入房间: ${roomName}`);
      }
    });

    // 离开房间
    socket.on('leave-room', (roomName) => {
      socket.leave(roomName);
      const clientInfo = this.connectedClients.get(socket.id);
      if (clientInfo) {
        clientInfo.rooms.delete(roomName);
      }
      socket.emit('room-left', { room: roomName });
      console.log(`📍 客户端 ${socket.id} 离开房间: ${roomName}`);
    });
  }

  // 验证房间名称
  isValidRoom(roomName) {
    const validRooms = ['admin', 'frontend', 'all'];
    return validRooms.includes(roomName);
  }

  // 广播数据变更事件
  broadcastDataChange(eventType, data, options = {}) {
    if (!this.io) {
      console.warn('WebSocket服务未初始化');
      return;
    }

    const event = {
      type: eventType,
      data,
      timestamp: new Date().toISOString(),
      source: options.source || 'server'
    };

    const targetRoom = options.room || 'all';
    
    if (targetRoom === 'all') {
      // 广播到所有客户端
      this.io.emit('data-change', event);
      console.log(`📡 广播事件到所有客户端: ${eventType}`);
    } else {
      // 广播到特定房间
      this.io.to(targetRoom).emit('data-change', event);
      console.log(`📡 广播事件到房间 ${targetRoom}: ${eventType}`);
    }
  }

  // 发送文章相关事件
  broadcastArticleChange(action, articleData, options = {}) {
    this.broadcastDataChange(`article:${action}`, articleData, options);
  }

  // 发送图片相关事件
  broadcastImageChange(action, imageData, options = {}) {
    this.broadcastDataChange(`image:${action}`, imageData, options);
  }

  // 发送系统事件
  broadcastSystemEvent(action, data = {}, options = {}) {
    this.broadcastDataChange(`system:${action}`, data, options);
  }

  // 获取连接统计
  getConnectionStats() {
    const stats = {
      totalConnections: this.connectedClients.size,
      adminConnections: 0,
      frontendConnections: 0,
      anonymousConnections: 0
    };

    this.connectedClients.forEach(client => {
      if (client.user.type === 'authenticated') {
        stats.adminConnections++;
      } else {
        stats.anonymousConnections++;
      }
      
      if (client.rooms.has('frontend')) {
        stats.frontendConnections++;
      }
    });

    return stats;
  }

  // 清除所有缓存
  clearAllCaches() {
    this.broadcastSystemEvent('cache_clear', {
      message: '系统缓存已清除，请刷新页面获取最新数据'
    });
  }

  // 获取WebSocket实例
  getIO() {
    return this.io;
  }
}

// 创建单例实例
const websocketService = new WebSocketService();

export default websocketService;