import mongoose from 'mongoose';
import dotenv from 'dotenv';

dotenv.config();

class DatabaseConfig {
  constructor() {
    this.isConnected = false;
    this.connectionRetries = 0;
    this.maxRetries = 5;
    this.retryDelay = 5000;
  }

  async connect() {
    const mongoUri = process.env.NODE_ENV === 'production' 
      ? process.env.MONGODB_URI_PROD 
      : process.env.MONGODB_URI;

    if (!mongoUri) {
      throw new Error('MongoDB URI 未配置，请检查环境变量');
    }

    const options = {
      maxPoolSize: 10,
      serverSelectionTimeoutMS: 5000,
      socketTimeoutMS: 45000,
      bufferCommands: false,
    };

    try {
      await mongoose.connect(mongoUri, options);
      this.isConnected = true;
      this.connectionRetries = 0;
      console.log('✅ MongoDB 连接成功');
      console.log(`📍 数据库: ${mongoose.connection.name}`);
    } catch (error) {
      console.error('❌ MongoDB 连接失败:', error.message);
      await this.handleConnectionError(error);
    }

    this.setupEventListeners();
  }

  async handleConnectionError(error) {
    this.isConnected = false;
    this.connectionRetries++;

    if (this.connectionRetries < this.maxRetries) {
      console.log(`🔄 尝试重新连接数据库 (${this.connectionRetries}/${this.maxRetries})...`);
      await new Promise(resolve => setTimeout(resolve, this.retryDelay));
      return this.connect();
    } else {
      console.error('❌ 数据库连接失败，已达到最大重试次数');
      throw error;
    }
  }

  setupEventListeners() {
    // 监听连接事件
    mongoose.connection.on('connected', () => {
      this.isConnected = true;
      console.log('📡 Mongoose 已连接到 MongoDB');
    });

    mongoose.connection.on('error', (error) => {
      this.isConnected = false;
      console.error('❌ Mongoose 连接错误:', error.message);
    });

    mongoose.connection.on('disconnected', () => {
      this.isConnected = false;
      console.log('📡 Mongoose 已断开连接');
      
      // 自动重连
      if (this.connectionRetries < this.maxRetries) {
        setTimeout(() => {
          console.log('🔄 尝试自动重连...');
          this.connect().catch(console.error);
        }, this.retryDelay);
      }
    });

    mongoose.connection.on('reconnected', () => {
      this.isConnected = true;
      console.log('✅ MongoDB 重连成功');
    });

    // 优雅关闭
    const gracefulShutdown = async (signal) => {
      console.log(`\n收到 ${signal} 信号，正在关闭数据库连接...`);
      try {
        await mongoose.connection.close();
        console.log('📡 MongoDB 连接已关闭');
        process.exit(0);
      } catch (error) {
        console.error('❌ 关闭数据库连接时出错:', error);
        process.exit(1);
      }
    };

    process.on('SIGINT', () => gracefulShutdown('SIGINT'));
    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
  }

  // 检查连接状态
  isHealthy() {
    return this.isConnected && mongoose.connection.readyState === 1;
  }

  // 获取连接信息
  getConnectionInfo() {
    return {
      isConnected: this.isConnected,
      readyState: mongoose.connection.readyState,
      host: mongoose.connection.host,
      port: mongoose.connection.port,
      name: mongoose.connection.name
    };
  }
}

export default DatabaseConfig;