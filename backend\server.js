import app from './app.js';
import DatabaseConfig from './config/database.js';
import websocketService from './services/websocketService.js';
import { createServer } from 'http';
import dotenv from 'dotenv';

// 加载环境变量
dotenv.config();

// 配置
const PORT = process.env.PORT || 5000;
const NODE_ENV = process.env.NODE_ENV || 'development';

// 启动服务器
const startServer = async () => {
  try {
    // 连接数据库（开发模式下可选）
    if (process.env.MONGODB_URI && process.env.MONGODB_URI !== 'mongodb://127.0.0.1:27017/moying-blog') {
      console.log('正在连接数据库...');
      const dbConfig = new DatabaseConfig();
      await dbConfig.connect();
      console.log('✅ 数据库连接成功');
    } else {
      console.log('⚠️ 跳过数据库连接（开发模式）');
    }
    
    // 创建HTTP服务器
    const server = createServer(app);
    
    // 初始化WebSocket服务
    websocketService.initialize(server);
    
    // 启动服务器
    server.listen(PORT, () => {
      console.log('\n🚀 墨影博客后端服务启动成功!');
      console.log(`📍 服务地址: http://localhost:${PORT}`);
      console.log(`🌍 运行环境: ${NODE_ENV}`);
      console.log(`📚 API文档: http://localhost:${PORT}/api`);
      console.log(`💚 健康检查: http://localhost:${PORT}/api/health`);
      console.log('\n可用的API端点:');
      console.log(`  🔐 认证相关: http://localhost:${PORT}/api/auth`);
      console.log(`  📝 文章管理: http://localhost:${PORT}/api/articles`);
      console.log(`  🖼️  图片管理: http://localhost:${PORT}/api/images`);
      console.log(`  👥 用户管理: http://localhost:${PORT}/api/users`);
      console.log('\n按 Ctrl+C 停止服务器\n');
    });
    
    // 保存服务器实例以便优雅关闭
    app.server = server;
    
    // 设置服务器超时
    server.timeout = 30000; // 30秒
    
    // 处理服务器错误
    server.on('error', (error) => {
      if (error.code === 'EADDRINUSE') {
        console.error(`❌ 端口 ${PORT} 已被占用，请尝试其他端口`);
        console.error('   可以通过设置环境变量 PORT 来指定其他端口');
        console.error(`   例如: PORT=3001 npm start`);
      } else {
        console.error('❌ 服务器启动失败:', error.message);
      }
      process.exit(1);
    });
    
    // 处理客户端连接错误
    server.on('clientError', (err, socket) => {
      console.error('客户端连接错误:', err.message);
      socket.end('HTTP/1.1 400 Bad Request\r\n\r\n');
    });
    
  } catch (error) {
    console.error('❌ 服务器启动失败:', error.message);
    
    // 根据错误类型提供具体的解决建议
    if (error.message.includes('ECONNREFUSED')) {
      console.error('\n💡 解决建议:');
      console.error('   1. 检查MongoDB是否正在运行');
      console.error('   2. 确认数据库连接字符串是否正确');
      console.error('   3. 检查网络连接');
    } else if (error.message.includes('authentication failed')) {
      console.error('\n💡 解决建议:');
      console.error('   1. 检查数据库用户名和密码');
      console.error('   2. 确认数据库用户权限');
    } else if (error.message.includes('ENOTFOUND')) {
      console.error('\n💡 解决建议:');
      console.error('   1. 检查数据库主机地址');
      console.error('   2. 确认网络连接');
    }
    
    console.error('\n请检查 .env 文件中的配置是否正确');
    process.exit(1);
  }
};

// 启动服务器
startServer();

// 开发环境下的额外配置
if (NODE_ENV === 'development') {
  // 监听文件变化（如果使用nodemon）
  process.on('SIGUSR2', () => {
    console.log('\n🔄 检测到文件变化，重启服务器...');
  });
  
  // 开发环境提示
  console.log('\n🛠️  开发模式已启用');
  console.log('   - 详细错误信息已开启');
  console.log('   - CORS策略已放宽');
  console.log('   - 请求日志已开启');
}

// 生产环境优化提示
if (NODE_ENV === 'production') {
  console.log('\n🏭 生产模式已启用');
  console.log('   - 安全策略已加强');
  console.log('   - 错误信息已简化');
  console.log('   - 性能优化已开启');
}