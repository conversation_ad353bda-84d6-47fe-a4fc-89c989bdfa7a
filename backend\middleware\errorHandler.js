import mongoose from 'mongoose';

// 开发环境错误详情
const isDevelopment = process.env.NODE_ENV === 'development';

// 错误类型映射
const errorTypes = {
  ValidationError: 400,
  CastError: 400,
  MongoServerError: 400,
  JsonWebTokenError: 401,
  TokenExpiredError: 401,
  MulterError: 400
};

// 处理MongoDB验证错误
const handleValidationError = (error) => {
  const errors = Object.values(error.errors).map(err => ({
    field: err.path,
    message: err.message,
    value: err.value
  }));
  
  return {
    statusCode: 400,
    message: '数据验证失败',
    errors
  };
};

// 处理MongoDB转换错误
const handleCastError = (error) => {
  return {
    statusCode: 400,
    message: `无效的${error.path}: ${error.value}`
  };
};

// 处理MongoDB重复键错误
const handleDuplicateKeyError = (error) => {
  const field = Object.keys(error.keyValue)[0];
  const value = error.keyValue[field];
  
  return {
    statusCode: 400,
    message: `${field} '${value}' 已存在`
  };
};

// 处理JWT错误
const handleJWTError = () => {
  return {
    statusCode: 401,
    message: '无效的令牌，请重新登录'
  };
};

// 处理JWT过期错误
const handleJWTExpiredError = () => {
  return {
    statusCode: 401,
    message: '令牌已过期，请重新登录'
  };
};

// 处理Multer文件上传错误
const handleMulterError = (error) => {
  let message = '文件上传失败';
  
  switch (error.code) {
    case 'LIMIT_FILE_SIZE':
      message = '文件大小超出限制';
      break;
    case 'LIMIT_FILE_COUNT':
      message = '文件数量超出限制';
      break;
    case 'LIMIT_UNEXPECTED_FILE':
      message = '意外的文件字段';
      break;
    case 'LIMIT_PART_COUNT':
      message = '表单部分数量超出限制';
      break;
    case 'LIMIT_FIELD_KEY':
      message = '字段名称过长';
      break;
    case 'LIMIT_FIELD_VALUE':
      message = '字段值过长';
      break;
    case 'LIMIT_FIELD_COUNT':
      message = '字段数量超出限制';
      break;
    case 'MISSING_FIELD_NAME':
      message = '缺少字段名称';
      break;
    default:
      message = error.message || '文件上传失败';
  }
  
  return {
    statusCode: 400,
    message
  };
};

// 自定义应用错误类
export class AppError extends Error {
  constructor(message, statusCode = 500, isOperational = true) {
    super(message);
    
    this.statusCode = statusCode;
    this.status = `${statusCode}`.startsWith('4') ? 'fail' : 'error';
    this.isOperational = isOperational;
    
    Error.captureStackTrace(this, this.constructor);
  }
}

// 异步错误捕获包装器
export const catchAsync = (fn) => {
  return (req, res, next) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

// 404错误处理中间件
export const notFound = (req, res, next) => {
  const error = new AppError(`未找到路由 ${req.originalUrl}`, 404);
  next(error);
};

// 全局错误处理中间件
export const globalErrorHandler = (err, req, res, next) => {
  let error = { ...err };
  error.message = err.message;
  
  // 记录错误日志
  console.error('错误详情:', {
    message: err.message,
    stack: err.stack,
    url: req.originalUrl,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    timestamp: new Date().toISOString()
  });
  
  // 处理不同类型的错误
  if (err.name === 'ValidationError') {
    const validationError = handleValidationError(err);
    error.statusCode = validationError.statusCode;
    error.message = validationError.message;
    error.errors = validationError.errors;
  }
  
  if (err.name === 'CastError') {
    const castError = handleCastError(err);
    error.statusCode = castError.statusCode;
    error.message = castError.message;
  }
  
  if (err.code === 11000) {
    const duplicateError = handleDuplicateKeyError(err);
    error.statusCode = duplicateError.statusCode;
    error.message = duplicateError.message;
  }
  
  if (err.name === 'JsonWebTokenError') {
    const jwtError = handleJWTError();
    error.statusCode = jwtError.statusCode;
    error.message = jwtError.message;
  }
  
  if (err.name === 'TokenExpiredError') {
    const jwtExpiredError = handleJWTExpiredError();
    error.statusCode = jwtExpiredError.statusCode;
    error.message = jwtExpiredError.message;
  }
  
  if (err.name === 'MulterError') {
    const multerError = handleMulterError(err);
    error.statusCode = multerError.statusCode;
    error.message = multerError.message;
  }
  
  // 设置默认状态码
  const statusCode = error.statusCode || errorTypes[err.name] || 500;
  
  // 构建错误响应
  const errorResponse = {
    success: false,
    message: error.message || '服务器内部错误',
    ...(error.errors && { errors: error.errors })
  };
  
  // 开发环境下添加错误堆栈
  if (isDevelopment) {
    errorResponse.stack = err.stack;
    errorResponse.error = err;
  }
  
  // 生产环境下隐藏敏感信息
  if (!isDevelopment && statusCode === 500) {
    errorResponse.message = '服务器内部错误';
  }
  
  res.status(statusCode).json(errorResponse);
};

// 未捕获异常处理
process.on('uncaughtException', (err) => {
  console.error('未捕获的异常:', err.name, err.message);
  console.error('错误堆栈:', err.stack);
  
  console.log('正在关闭应用程序...');
  process.exit(1);
});

// 未处理的Promise拒绝
process.on('unhandledRejection', (err, promise) => {
  console.error('未处理的Promise拒绝:', err.name, err.message);
  console.error('Promise:', promise);
  
  console.log('正在关闭服务器...');
  // 优雅关闭服务器
  if (global.server) {
    global.server.close(() => {
      console.log('服务器已关闭');
      process.exit(1);
    });
  } else {
    process.exit(1);
  }
});

// SIGTERM信号处理
process.on('SIGTERM', () => {
  console.log('收到SIGTERM信号，正在优雅关闭...');
  
  if (global.server) {
    global.server.close(() => {
      console.log('服务器已关闭');
      process.exit(0);
    });
  } else {
    process.exit(0);
  }
});

// SIGINT信号处理（Ctrl+C）
process.on('SIGINT', () => {
  console.log('收到SIGINT信号，正在优雅关闭...');
  
  if (global.server) {
    global.server.close(() => {
      console.log('服务器已关闭');
      process.exit(0);
    });
  } else {
    process.exit(0);
  }
});

// 导出错误处理相关功能
export default {
  AppError,
  catchAsync,
  notFound,
  globalErrorHandler
};