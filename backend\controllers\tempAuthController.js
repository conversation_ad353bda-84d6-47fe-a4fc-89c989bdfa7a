import { testUsers } from '../data/testUsers.js';
import jwtConfig from '../config/jwt.js';
import { AppError, catchAsync } from '../middleware/errorHandler.js';
import bcrypt from 'bcryptjs';

// 临时登录控制器（不依赖数据库）
export const tempLogin = catchAsync(async (req, res, next) => {
  const { identifier, password } = req.body;
  
  console.log('登录尝试:', { identifier, password });
  
  // 在测试用户中查找
  const user = testUsers.find(u => 
    u.username === identifier || u.email === identifier
  );
  
  if (!user) {
    console.log('用户未找到:', identifier);
    return next(new AppError('用户名或密码错误', 401));
  }
  
  console.log('找到用户:', user.username);
  
  // 检查用户状态
  if (user.status !== 'active') {
    return next(new AppError('账户已被禁用，请联系管理员', 401));
  }
  
  // 验证密码
  const isPasswordCorrect = await bcrypt.compare(password, user.password);
  console.log('密码验证结果:', isPasswordCorrect);
  
  if (!isPasswordCorrect) {
    return next(new AppError('用户名或密码错误', 401));
  }
  
  // 生成JWT令牌
  const tokens = jwtConfig.generateTokenPair({ userId: user._id });
  
  // 移除密码等敏感信息
  const userResponse = { ...user };
  delete userResponse.password;
  
  console.log('登录成功，返回用户信息');
  
  res.status(200).json({
    success: true,
    message: '登录成功',
    data: {
      user: userResponse,
      token: tokens.accessToken, // 前端期望的token格式
      permissions: user.permissions || [] // 添加权限字段
    }
  });
});

export default {
  tempLogin
};