import React, { useState } from 'react';
import { Layout, Menu, Avatar, Dropdown, <PERSON><PERSON>, <PERSON><PERSON>, Drawer, Typography } from 'antd';
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  DashboardOutlined,
  FileTextOutlined,
  PictureOutlined,
  UserOutlined,
  SettingOutlined,
  LogoutOutlined,
  BellOutlined,
  SearchOutlined,
  FullscreenOutlined,
  FullscreenExitOutlined
} from '@ant-design/icons';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuthStore } from '@/store/authStore';
import { useWindowSize } from '@/hooks';

const { Header, Sider, Content } = Layout;
const { Text } = Typography;

const DashboardLayout = ({ children }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const { user, logout, hasPermission } = useAuthStore();
  const { isMobile } = useWindowSize();
  
  const [collapsed, setCollapsed] = useState(isMobile);
  const [mobileDrawerVisible, setMobileDrawerVisible] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);
  
  // 菜单项配置
  const menuItems = [
    {
      key: '/dashboard',
      icon: <DashboardOutlined />,
      label: '仪表板'
    },
    {
      key: '/articles',
      icon: <FileTextOutlined />,
      label: '文章管理',
      children: [
        {
          key: '/articles',
          label: '文章列表'
        },
        {
          key: '/articles/new',
          label: '新建文章'
        }
      ]
    },
    {
      key: '/images',
      icon: <PictureOutlined />,
      label: '图片管理'
    },
    {
      key: '/users',
      icon: <UserOutlined />,
      label: '用户管理',
      hidden: !hasPermission('user:read')
    },
    {
      key: '/settings',
      icon: <SettingOutlined />,
      label: '系统设置',
      hidden: !hasPermission('system:settings')
    }
  ].filter(item => !item.hidden);
  
  // 用户菜单
  const userMenuItems = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: '个人资料',
      onClick: () => navigate('/profile')
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: '账户设置',
      onClick: () => navigate('/profile')
    },
    {
      type: 'divider'
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
      onClick: handleLogout
    }
  ];
  
  // 处理菜单点击
  const handleMenuClick = ({ key }) => {
    navigate(key);
    if (isMobile) {
      setMobileDrawerVisible(false);
    }
  };
  
  // 处理登出
  async function handleLogout() {
    await logout();
    navigate('/login');
  }
  
  // 切换全屏
  const toggleFullscreen = () => {
    if (!document.fullscreenElement) {
      document.documentElement.requestFullscreen();
      setIsFullscreen(true);
    } else {
      document.exitFullscreen();
      setIsFullscreen(false);
    }
  };
  
  // 监听全屏状态变化
  React.useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement);
    };
    
    document.addEventListener('fullscreenchange', handleFullscreenChange);
    return () => document.removeEventListener('fullscreenchange', handleFullscreenChange);
  }, []);
  
  // 侧边栏内容
  const siderContent = (
    <>
      {/* Logo */}
      <div style={{
        height: '64px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: collapsed ? 'center' : 'flex-start',
        padding: collapsed ? '0' : '0 24px',
        background: 'rgba(255, 255, 255, 0.1)',
        borderBottom: '1px solid rgba(255, 255, 255, 0.1)'
      }}>
        <div style={{
          width: '32px',
          height: '32px',
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          borderRadius: '8px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          color: 'white',
          fontSize: '16px',
          fontWeight: 'bold'
        }}>
          M
        </div>
        {!collapsed && (
          <Text style={{
            color: 'white',
            fontSize: '18px',
            fontWeight: 600,
            marginLeft: '12px'
          }}>
            墨影博客
          </Text>
        )}
      </div>
      
      {/* 菜单 */}
      <Menu
        theme="dark"
        mode="inline"
        selectedKeys={[location.pathname]}
        defaultOpenKeys={['/articles']}
        items={menuItems}
        onClick={handleMenuClick}
        style={{
          border: 'none',
          background: 'transparent'
        }}
      />
    </>
  );
  
  return (
    <Layout style={{ minHeight: '100vh' }}>
      {/* 桌面端侧边栏 */}
      {!isMobile && (
        <Sider
          trigger={null}
          collapsible
          collapsed={collapsed}
          width={256}
          style={{
            background: 'linear-gradient(180deg, #1f2937 0%, #111827 100%)',
            boxShadow: '2px 0 8px rgba(0, 0, 0, 0.1)'
          }}
        >
          {siderContent}
        </Sider>
      )}
      
      {/* 移动端抽屉 */}
      {isMobile && (
        <Drawer
          title={null}
          placement="left"
          closable={false}
          onClose={() => setMobileDrawerVisible(false)}
          open={mobileDrawerVisible}
          bodyStyle={{ padding: 0 }}
          width={256}
        >
          <div style={{
            background: 'linear-gradient(180deg, #1f2937 0%, #111827 100%)',
            height: '100%'
          }}>
            {siderContent}
          </div>
        </Drawer>
      )}
      
      <Layout>
        {/* 头部 */}
        <Header style={{
          padding: '0 24px',
          background: '#fff',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          boxShadow: '0 1px 4px rgba(0, 0, 0, 0.08)',
          zIndex: 1
        }}>
          {/* 左侧 */}
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <Button
              type="text"
              icon={isMobile ? <MenuUnfoldOutlined /> : (collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />)}
              onClick={() => {
                if (isMobile) {
                  setMobileDrawerVisible(true);
                } else {
                  setCollapsed(!collapsed);
                }
              }}
              style={{
                fontSize: '16px',
                width: 40,
                height: 40
              }}
            />
            
            {/* 面包屑或页面标题可以在这里添加 */}
          </div>
          
          {/* 右侧 */}
          <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
            {/* 搜索 */}
            <Button
              type="text"
              icon={<SearchOutlined />}
              style={{ fontSize: '16px' }}
              className="hidden-xs"
            />
            
            {/* 全屏 */}
            <Button
              type="text"
              icon={isFullscreen ? <FullscreenExitOutlined /> : <FullscreenOutlined />}
              onClick={toggleFullscreen}
              style={{ fontSize: '16px' }}
              className="hidden-xs"
            />
            
            {/* 通知 */}
            <Badge count={5} size="small">
              <Button
                type="text"
                icon={<BellOutlined />}
                style={{ fontSize: '16px' }}
              />
            </Badge>
            
            {/* 用户信息 */}
            <Dropdown
              menu={{ items: userMenuItems }}
              placement="bottomRight"
              trigger={['click']}
            >
              <div style={{
                display: 'flex',
                alignItems: 'center',
                cursor: 'pointer',
                padding: '4px 8px',
                borderRadius: '8px',
                transition: 'background-color 0.3s'
              }}>
                <Avatar
                  size={32}
                  src={user?.avatar}
                  style={{
                    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
                  }}
                >
                  {user?.username?.[0]?.toUpperCase()}
                </Avatar>
                <div style={{
                  marginLeft: '8px',
                  display: isMobile ? 'none' : 'block'
                }}>
                  <Text style={{ fontSize: '14px', fontWeight: 500 }}>
                    {user?.username || '用户'}
                  </Text>
                  <br />
                  <Text type="secondary" style={{ fontSize: '12px' }}>
                    {user?.role === 'admin' ? '管理员' : '编辑'}
                  </Text>
                </div>
              </div>
            </Dropdown>
          </div>
        </Header>
        
        {/* 内容区域 */}
        <Content style={{
          margin: '24px',
          padding: '24px',
          background: '#fff',
          borderRadius: '8px',
          minHeight: 'calc(100vh - 112px)',
          overflow: 'auto'
        }}>
          {children}
        </Content>
      </Layout>
    </Layout>
  );
};

export default DashboardLayout;