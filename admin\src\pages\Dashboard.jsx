import React from 'react';
import { Row, Col, Card, Statistic, Progress, List, Avatar, Tag, Button, Typography, Space } from 'antd';
import {
  FileTextOutlined,
  PictureOutlined,
  UserOutlined,
  EyeOutlined,
  LikeOutlined,
  MessageOutlined,
  TrophyOutlined,
  RiseOutlined,
  FallOutlined
} from '@ant-design/icons';
import { useAsync } from '@/hooks';
import { articleAPI, imageAPI, userAPI, systemAPI } from '@/services/api';
import { formatDate, formatNumber } from '@/utils';

const { Title, Text } = Typography;

const Dashboard = () => {
  // 获取统计数据
  const { data: articleStats, loading: articleStatsLoading } = useAsync(
    () => articleAPI.getStats(),
    [],
    { initialData: {} }
  );
  
  const { data: imageStats, loading: imageStatsLoading } = useAsync(
    () => imageAPI.getStats(),
    [],
    { initialData: {} }
  );
  
  const { data: userStats, loading: userStatsLoading } = useAsync(
    () => userAPI.getStats(),
    [],
    { initialData: {} }
  );
  
  const { data: systemStats, loading: systemStatsLoading } = useAsync(
    () => systemAPI.getStats(),
    [],
    { initialData: {} }
  );
  
  // 获取最新文章
  const { data: recentArticles, loading: recentArticlesLoading } = useAsync(
    () => articleAPI.getList({ page: 1, pageSize: 5, sortBy: 'createdAt', sortOrder: 'desc' }),
    [],
    { initialData: { data: [] } }
  );
  
  // 统计卡片数据
  const statsCards = [
    {
      title: '文章总数',
      value: articleStats.total || 0,
      change: articleStats.changePercent || 0,
      icon: <FileTextOutlined style={{ color: '#1890ff' }} />,
      color: '#1890ff',
      loading: articleStatsLoading
    },
    {
      title: '图片总数',
      value: imageStats.total || 0,
      change: imageStats.changePercent || 0,
      icon: <PictureOutlined style={{ color: '#52c41a' }} />,
      color: '#52c41a',
      loading: imageStatsLoading
    },
    {
      title: '用户总数',
      value: userStats.total || 0,
      change: userStats.changePercent || 0,
      icon: <UserOutlined style={{ color: '#722ed1' }} />,
      color: '#722ed1',
      loading: userStatsLoading
    },
    {
      title: '总访问量',
      value: systemStats.totalViews || 0,
      change: systemStats.viewsChangePercent || 0,
      icon: <EyeOutlined style={{ color: '#fa8c16' }} />,
      color: '#fa8c16',
      loading: systemStatsLoading
    }
  ];
  
  // 热门文章数据（模拟）
  const popularArticles = [
    {
      id: 1,
      title: 'React 18 新特性详解',
      views: 1234,
      likes: 89,
      comments: 23,
      author: '张三',
      publishedAt: '2024-01-15'
    },
    {
      id: 2,
      title: 'Vue 3 Composition API 实战',
      views: 987,
      likes: 67,
      comments: 18,
      author: '李四',
      publishedAt: '2024-01-14'
    },
    {
      id: 3,
      title: 'TypeScript 高级类型应用',
      views: 756,
      likes: 45,
      comments: 12,
      author: '王五',
      publishedAt: '2024-01-13'
    }
  ];
  
  // 系统活动数据（模拟）
  const systemActivities = [
    {
      id: 1,
      type: 'article',
      action: '发布了新文章',
      title: 'React 性能优化最佳实践',
      user: '张三',
      time: '2024-01-15 14:30:00'
    },
    {
      id: 2,
      type: 'user',
      action: '新用户注册',
      title: '用户 "新手小白" 加入了系统',
      user: '系统',
      time: '2024-01-15 13:45:00'
    },
    {
      id: 3,
      type: 'image',
      action: '上传了图片',
      title: '上传了 5 张新图片',
      user: '李四',
      time: '2024-01-15 12:20:00'
    },
    {
      id: 4,
      type: 'article',
      action: '更新了文章',
      title: 'Vue 3 组件设计模式',
      user: '王五',
      time: '2024-01-15 11:15:00'
    }
  ];
  
  const getActivityIcon = (type) => {
    switch (type) {
      case 'article':
        return <FileTextOutlined style={{ color: '#1890ff' }} />;
      case 'user':
        return <UserOutlined style={{ color: '#722ed1' }} />;
      case 'image':
        return <PictureOutlined style={{ color: '#52c41a' }} />;
      default:
        return <FileTextOutlined />;
    }
  };
  
  return (
    <div>
      {/* 页面标题 */}
      <div style={{ marginBottom: '24px' }}>
        <Title level={2} style={{ margin: 0 }}>仪表板</Title>
        <Text type="secondary">欢迎回来，这里是您的博客管理概览</Text>
      </div>
      
      {/* 统计卡片 */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        {statsCards.map((card, index) => (
          <Col xs={24} sm={12} lg={6} key={index}>
            <Card
              loading={card.loading}
              style={{
                background: `linear-gradient(135deg, ${card.color}15 0%, ${card.color}05 100%)`,
                border: `1px solid ${card.color}20`,
                borderRadius: '12px'
              }}
            >
              <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <div>
                  <Text type="secondary" style={{ fontSize: '14px' }}>
                    {card.title}
                  </Text>
                  <div style={{ fontSize: '28px', fontWeight: 'bold', color: card.color, marginTop: '8px' }}>
                    {formatNumber(card.value)}
                  </div>
                  <div style={{ marginTop: '8px', display: 'flex', alignItems: 'center' }}>
                    {card.change >= 0 ? (
                      <RiseOutlined style={{ color: '#52c41a', marginRight: '4px' }} />
                    ) : (
                      <FallOutlined style={{ color: '#ff4d4f', marginRight: '4px' }} />
                    )}
                    <Text
                      style={{
                        color: card.change >= 0 ? '#52c41a' : '#ff4d4f',
                        fontSize: '12px'
                      }}
                    >
                      {Math.abs(card.change)}% 较上月
                    </Text>
                  </div>
                </div>
                <div style={{ fontSize: '32px', opacity: 0.8 }}>
                  {card.icon}
                </div>
              </div>
            </Card>
          </Col>
        ))}
      </Row>
      
      <Row gutter={[16, 16]}>
        {/* 最新文章 */}
        <Col xs={24} lg={12}>
          <Card
            title="最新文章"
            extra={<Button type="link">查看全部</Button>}
            style={{ height: '400px' }}
          >
            <List
              loading={recentArticlesLoading}
              dataSource={recentArticles.data}
              renderItem={(article) => (
                <List.Item>
                  <List.Item.Meta
                    avatar={
                      <Avatar
                        shape="square"
                        size={48}
                        src={article.coverImage}
                        icon={<FileTextOutlined />}
                        style={{ background: '#f0f0f0' }}
                      />
                    }
                    title={
                      <Text strong style={{ fontSize: '14px' }}>
                        {article.title}
                      </Text>
                    }
                    description={
                      <Space size={16}>
                        <Text type="secondary" style={{ fontSize: '12px' }}>
                          {formatDate(article.createdAt, 'MM-DD HH:mm')}
                        </Text>
                        <Tag color={article.status === 'published' ? 'green' : 'orange'}>
                          {article.status === 'published' ? '已发布' : '草稿'}
                        </Tag>
                      </Space>
                    }
                  />
                </List.Item>
              )}
            />
          </Card>
        </Col>
        
        {/* 热门文章 */}
        <Col xs={24} lg={12}>
          <Card
            title="热门文章"
            extra={<Button type="link">查看全部</Button>}
            style={{ height: '400px' }}
          >
            <List
              dataSource={popularArticles}
              renderItem={(article, index) => (
                <List.Item>
                  <List.Item.Meta
                    avatar={
                      <div style={{
                        width: '32px',
                        height: '32px',
                        borderRadius: '50%',
                        background: index < 3 ? '#faad14' : '#d9d9d9',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        color: 'white',
                        fontWeight: 'bold'
                      }}>
                        {index < 3 ? <TrophyOutlined /> : index + 1}
                      </div>
                    }
                    title={
                      <Text strong style={{ fontSize: '14px' }}>
                        {article.title}
                      </Text>
                    }
                    description={
                      <Space size={12}>
                        <Space size={4}>
                          <EyeOutlined style={{ fontSize: '12px' }} />
                          <Text type="secondary" style={{ fontSize: '12px' }}>
                            {formatNumber(article.views)}
                          </Text>
                        </Space>
                        <Space size={4}>
                          <LikeOutlined style={{ fontSize: '12px' }} />
                          <Text type="secondary" style={{ fontSize: '12px' }}>
                            {formatNumber(article.likes)}
                          </Text>
                        </Space>
                        <Space size={4}>
                          <MessageOutlined style={{ fontSize: '12px' }} />
                          <Text type="secondary" style={{ fontSize: '12px' }}>
                            {formatNumber(article.comments)}
                          </Text>
                        </Space>
                      </Space>
                    }
                  />
                </List.Item>
              )}
            />
          </Card>
        </Col>
        
        {/* 系统活动 */}
        <Col xs={24} lg={12}>
          <Card
            title="系统活动"
            extra={<Button type="link">查看全部</Button>}
            style={{ height: '400px' }}
          >
            <List
              dataSource={systemActivities}
              renderItem={(activity) => (
                <List.Item>
                  <List.Item.Meta
                    avatar={getActivityIcon(activity.type)}
                    title={
                      <Text style={{ fontSize: '14px' }}>
                        <Text strong>{activity.user}</Text> {activity.action}
                      </Text>
                    }
                    description={
                      <div>
                        <Text type="secondary" style={{ fontSize: '13px' }}>
                          {activity.title}
                        </Text>
                        <br />
                        <Text type="secondary" style={{ fontSize: '12px' }}>
                          {formatDate(activity.time, 'MM-DD HH:mm')}
                        </Text>
                      </div>
                    }
                  />
                </List.Item>
              )}
            />
          </Card>
        </Col>
        
        {/* 存储使用情况 */}
        <Col xs={24} lg={12}>
          <Card title="存储使用情况" style={{ height: '400px' }}>
            <div style={{ marginBottom: '24px' }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '8px' }}>
                <Text>图片存储</Text>
                <Text>2.3GB / 10GB</Text>
              </div>
              <Progress percent={23} strokeColor="#52c41a" />
            </div>
            
            <div style={{ marginBottom: '24px' }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '8px' }}>
                <Text>数据库</Text>
                <Text>156MB / 1GB</Text>
              </div>
              <Progress percent={15.6} strokeColor="#1890ff" />
            </div>
            
            <div style={{ marginBottom: '24px' }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '8px' }}>
                <Text>日志文件</Text>
                <Text>89MB / 500MB</Text>
              </div>
              <Progress percent={17.8} strokeColor="#faad14" />
            </div>
            
            <div>
              <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '8px' }}>
                <Text>缓存</Text>
                <Text>234MB / 2GB</Text>
              </div>
              <Progress percent={11.7} strokeColor="#722ed1" />
            </div>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default Dashboard;