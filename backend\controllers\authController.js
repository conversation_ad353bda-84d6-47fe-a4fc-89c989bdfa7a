import User from '../models/User.js';
import jwtConfig from '../config/jwt.js';
import { AppError, catchAsync } from '../middleware/errorHandler.js';
import bcrypt from 'bcryptjs';

// 生成并发送令牌响应
const createSendToken = async (user, statusCode, res, message = '操作成功') => {
  const tokens = jwtConfig.generateTokenPair(user._id);
  
  // 保存刷新令牌到用户记录
  await user.addRefreshToken(tokens.refreshToken);
  
  // 移除密码等敏感信息
  const userResponse = user.toObject();
  delete userResponse.password;
  delete userResponse.refreshTokens;
  
  res.status(statusCode).json({
    success: true,
    message,
    data: {
      user: userResponse,
      tokens
    }
  });
};

// 用户注册
export const register = catchAsync(async (req, res, next) => {
  const { username, email, password, displayName, bio } = req.body;
  
  // 检查用户名和邮箱是否已存在
  const existingUser = await User.findOne({
    $or: [{ username }, { email }]
  });
  
  if (existingUser) {
    if (existingUser.username === username) {
      return next(new AppError('用户名已存在', 400));
    }
    if (existingUser.email === email) {
      return next(new AppError('邮箱已存在', 400));
    }
  }
  
  // 创建新用户
  const newUser = await User.create({
    username,
    email,
    password,
    displayName: displayName || username,
    bio: bio || ''
  });
  
  // 生成令牌并响应
  await createSendToken(newUser, 201, res, '注册成功');
});

// 用户登录
export const login = catchAsync(async (req, res, next) => {
  const { identifier, password } = req.body;
  
  // 查找用户（通过用户名或邮箱）
  const user = await User.findOne({
    $or: [
      { username: identifier },
      { email: identifier }
    ]
  }).select('+password');
  
  if (!user) {
    // 记录失败的登录尝试
    if (req.recordFailedLogin) {
      req.recordFailedLogin();
    }
    return next(new AppError('用户名或密码错误', 401));
  }
  
  // 检查用户状态
  if (user.status !== 'active') {
    return next(new AppError('账户已被禁用，请联系管理员', 401));
  }
  
  // 验证密码
  const isPasswordCorrect = await user.comparePassword(password);
  if (!isPasswordCorrect) {
    // 记录失败的登录尝试
    if (req.recordFailedLogin) {
      req.recordFailedLogin();
    }
    return next(new AppError('用户名或密码错误', 401));
  }
  
  // 清除登录尝试记录
  if (req.clearLoginAttempts) {
    req.clearLoginAttempts();
  }
  
  // 更新最后登录时间
  user.lastLoginAt = new Date();
  await user.save({ validateBeforeSave: false });
  
  // 生成令牌并响应
  await createSendToken(user, 200, res, '登录成功');
});

// 刷新令牌
export const refreshToken = catchAsync(async (req, res, next) => {
  const { user, refreshToken: oldRefreshToken } = req;
  
  // 生成新的令牌对
  const tokens = jwtConfig.generateTokenPair(user._id);
  
  // 替换旧的刷新令牌
  await user.replaceRefreshToken(oldRefreshToken, tokens.refreshToken);
  
  res.status(200).json({
    success: true,
    message: '令牌刷新成功',
    data: {
      tokens
    }
  });
});

// 用户登出
export const logout = catchAsync(async (req, res, next) => {
  const { user, token } = req;
  const { refreshToken } = req.body;
  
  // 移除刷新令牌
  if (refreshToken) {
    await user.removeRefreshToken(refreshToken);
  }
  
  res.status(200).json({
    success: true,
    message: '登出成功'
  });
});

// 登出所有设备
export const logoutAll = catchAsync(async (req, res, next) => {
  const { user } = req;
  
  // 清除所有刷新令牌
  user.refreshTokens = [];
  await user.save({ validateBeforeSave: false });
  
  res.status(200).json({
    success: true,
    message: '已从所有设备登出'
  });
});

// 获取当前用户信息
export const getMe = catchAsync(async (req, res, next) => {
  const user = req.user.toObject();
  delete user.password;
  delete user.refreshTokens;
  
  res.status(200).json({
    success: true,
    data: {
      user
    }
  });
});

// 更新用户信息
export const updateMe = catchAsync(async (req, res, next) => {
  const { user } = req;
  const allowedFields = ['displayName', 'bio', 'avatar', 'website', 'location'];
  
  // 过滤允许更新的字段
  const updates = {};
  Object.keys(req.body).forEach(key => {
    if (allowedFields.includes(key)) {
      updates[key] = req.body[key];
    }
  });
  
  // 更新用户信息
  Object.assign(user, updates);
  await user.save();
  
  // 返回更新后的用户信息
  const updatedUser = user.toObject();
  delete updatedUser.password;
  delete updatedUser.refreshTokens;
  
  res.status(200).json({
    success: true,
    message: '用户信息更新成功',
    data: {
      user: updatedUser
    }
  });
});

// 修改密码
export const changePassword = catchAsync(async (req, res, next) => {
  const { user } = req;
  const { currentPassword, newPassword } = req.body;
  
  // 获取包含密码的用户信息
  const userWithPassword = await User.findById(user._id).select('+password');
  
  // 验证当前密码
  const isCurrentPasswordCorrect = await userWithPassword.comparePassword(currentPassword);
  if (!isCurrentPasswordCorrect) {
    return next(new AppError('当前密码错误', 400));
  }
  
  // 检查新密码是否与当前密码相同
  const isSamePassword = await userWithPassword.comparePassword(newPassword);
  if (isSamePassword) {
    return next(new AppError('新密码不能与当前密码相同', 400));
  }
  
  // 更新密码
  userWithPassword.password = newPassword;
  await userWithPassword.save();
  
  // 清除所有刷新令牌（强制重新登录）
  userWithPassword.refreshTokens = [];
  await userWithPassword.save({ validateBeforeSave: false });
  
  res.status(200).json({
    success: true,
    message: '密码修改成功，请重新登录'
  });
});

// 删除账户
export const deleteAccount = catchAsync(async (req, res, next) => {
  const { user } = req;
  const { password } = req.body;
  
  if (!password) {
    return next(new AppError('请提供密码以确认删除操作', 400));
  }
  
  // 获取包含密码的用户信息
  const userWithPassword = await User.findById(user._id).select('+password');
  
  // 验证密码
  const isPasswordCorrect = await userWithPassword.comparePassword(password);
  if (!isPasswordCorrect) {
    return next(new AppError('密码错误', 400));
  }
  
  // 软删除用户（标记为已删除）
  userWithPassword.status = 'deleted';
  userWithPassword.deletedAt = new Date();
  userWithPassword.refreshTokens = [];
  await userWithPassword.save({ validateBeforeSave: false });
  
  res.status(200).json({
    success: true,
    message: '账户删除成功'
  });
});

// 检查用户名可用性
export const checkUsername = catchAsync(async (req, res, next) => {
  const { username } = req.params;
  
  const existingUser = await User.findOne({ username });
  const isAvailable = !existingUser;
  
  res.status(200).json({
    success: true,
    data: {
      username,
      available: isAvailable
    }
  });
});

// 检查邮箱可用性
export const checkEmail = catchAsync(async (req, res, next) => {
  const { email } = req.params;
  
  const existingUser = await User.findOne({ email });
  const isAvailable = !existingUser;
  
  res.status(200).json({
    success: true,
    data: {
      email,
      available: isAvailable
    }
  });
});

// 获取用户统计信息
export const getUserStats = catchAsync(async (req, res, next) => {
  const { user } = req;
  
  // 这里可以添加更多统计信息，比如文章数量、评论数量等
  const stats = {
    joinDate: user.createdAt,
    lastLogin: user.lastLoginAt,
    articlesCount: 0, // 待实现
    commentsCount: 0, // 待实现
    likesReceived: 0  // 待实现
  };
  
  res.status(200).json({
    success: true,
    data: {
      stats
    }
  });
});

// 验证令牌（用于前端检查登录状态）
export const verifyToken = catchAsync(async (req, res, next) => {
  // 如果能到达这里，说明令牌有效（通过了authenticate中间件）
  const user = req.user.toObject();
  delete user.password;
  delete user.refreshTokens;
  
  res.status(200).json({
    success: true,
    message: '令牌有效',
    data: {
      user
    }
  });
});

// 导出所有认证控制器
export default {
  register,
  login,
  refreshToken,
  logout,
  logoutAll,
  getMe,
  updateMe,
  changePassword,
  deleteAccount,
  checkUsername,
  checkEmail,
  getUserStats,
  verifyToken
};