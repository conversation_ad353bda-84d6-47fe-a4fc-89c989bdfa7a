import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { authAPI } from '@/services/api';
import { message } from 'antd';

const useAuthStore = create(
  persist(
    (set, get) => ({
      // 状态
      user: null,
      token: null,
      isAuthenticated: false,
      isLoading: false,
      permissions: [],
      
      // 登录
      login: async (credentials) => {
        try {
          set({ isLoading: true });
          
          const response = await authAPI.login(credentials);
          const { user, token, permissions } = response.data;
          
          set({
            user,
            token,
            permissions: permissions || [],
            isAuthenticated: true,
            isLoading: false
          });
          
          // 设置 axios 默认 token
          if (token) {
            localStorage.setItem('token', token);
            // 这里可以设置 axios 默认 headers
          }
          
          message.success('登录成功');
          return { success: true, data: response.data };
          
        } catch (error) {
          set({ isLoading: false });
          const errorMessage = error.response?.data?.message || '登录失败';
          message.error(errorMessage);
          return { success: false, error: errorMessage };
        }
      },
      
      // 登出
      logout: async () => {
        try {
          // 调用登出 API（可选）
          await authAPI.logout().catch(() => {});
        } catch (error) {
          console.warn('Logout API call failed:', error);
        } finally {
          // 清除本地状态
          set({
            user: null,
            token: null,
            isAuthenticated: false,
            permissions: []
          });
          
          // 清除本地存储
          localStorage.removeItem('token');
          
          message.success('已退出登录');
        }
      },
      
      // 刷新用户信息
      refreshUser: async () => {
        try {
          const response = await authAPI.getProfile();
          const { user, permissions } = response.data;
          
          set({
            user,
            permissions: permissions || []
          });
          
          return { success: true, data: response.data };
        } catch (error) {
          console.error('Failed to refresh user:', error);
          // 如果刷新失败，可能是 token 过期，执行登出
          if (error.response?.status === 401) {
            get().logout();
          }
          return { success: false, error: error.message };
        }
      },
      
      // 更新用户信息
      updateUser: (userData) => {
        set((state) => ({
          user: { ...state.user, ...userData }
        }));
      },
      
      // 检查权限
      hasPermission: (permission) => {
        const { permissions, user } = get();
        
        // 超级管理员拥有所有权限
        if (user?.role === 'admin') {
          return true;
        }
        
        // 检查具体权限
        return permissions.includes(permission);
      },
      
      // 检查角色
      hasRole: (role) => {
        const { user } = get();
        return user?.role === role;
      },
      
      // 初始化认证状态
      initializeAuth: async () => {
        const token = localStorage.getItem('token');
        
        if (!token) {
          set({ isLoading: false });
          return;
        }
        
        try {
          set({ isLoading: true, token });
          
          // 验证 token 并获取用户信息
          const response = await authAPI.getProfile();
          const { user, permissions } = response.data;
          
          set({
            user,
            permissions: permissions || [],
            isAuthenticated: true,
            isLoading: false
          });
          
        } catch (error) {
          console.error('Auth initialization failed:', error);
          
          // Token 无效，清除状态
          set({
            user: null,
            token: null,
            isAuthenticated: false,
            permissions: [],
            isLoading: false
          });
          
          localStorage.removeItem('token');
        }
      },
      
      // 更新密码
      updatePassword: async (passwordData) => {
        try {
          set({ isLoading: true });
          
          await authAPI.updatePassword(passwordData);
          
          set({ isLoading: false });
          message.success('密码更新成功');
          return { success: true };
          
        } catch (error) {
          set({ isLoading: false });
          const errorMessage = error.response?.data?.message || '密码更新失败';
          message.error(errorMessage);
          return { success: false, error: errorMessage };
        }
      },
      
      // 重置状态（用于测试或特殊情况）
      reset: () => {
        set({
          user: null,
          token: null,
          isAuthenticated: false,
          isLoading: false,
          permissions: []
        });
        localStorage.removeItem('token');
      }
    }),
    {
      name: 'auth-storage',
      // 只持久化必要的状态
      partialize: (state) => ({
        user: state.user,
        token: state.token,
        isAuthenticated: state.isAuthenticated,
        permissions: state.permissions
      }),
      // 版本控制，用于处理存储结构变更
      version: 1,
      migrate: (persistedState, version) => {
        if (version === 0) {
          // 从版本 0 迁移到版本 1 的逻辑
          return {
            ...persistedState,
            permissions: persistedState.permissions || []
          };
        }
        return persistedState;
      }
    }
  )
);

export { useAuthStore };

// 权限常量
export const PERMISSIONS = {
  // 文章权限
  ARTICLE_CREATE: 'article:create',
  ARTICLE_READ: 'article:read',
  ARTICLE_UPDATE: 'article:update',
  ARTICLE_DELETE: 'article:delete',
  ARTICLE_PUBLISH: 'article:publish',
  
  // 图片权限
  IMAGE_UPLOAD: 'image:upload',
  IMAGE_READ: 'image:read',
  IMAGE_UPDATE: 'image:update',
  IMAGE_DELETE: 'image:delete',
  
  // 用户权限
  USER_CREATE: 'user:create',
  USER_READ: 'user:read',
  USER_UPDATE: 'user:update',
  USER_DELETE: 'user:delete',
  
  // 系统权限
  SYSTEM_SETTINGS: 'system:settings',
  SYSTEM_LOGS: 'system:logs',
  SYSTEM_STATS: 'system:stats'
};

// 角色常量
export const ROLES = {
  ADMIN: 'admin',
  EDITOR: 'editor',
  AUTHOR: 'author',
  USER: 'user'
};